"""
测试错误处理和异常情况
"""
import asyncio
import pytest
import numpy as np
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime, timedelta

from server.session_manager import SessionManager, ProcessorFactory
from server.video_processing_manager import VideoProcessingManager, VideoDecoder
from models import (
    Session, SessionStatus, ProcessingResult, 
    SessionQueue, FrameQueue, VideoProcessor
)
from processors.processors import DummyProcessor, OpenCVPreviewProcessor


class TestSessionManagerErrorHandling:
    """测试SessionManager的错误处理"""
    
    @pytest.mark.asyncio
    async def test_invalid_analysis_type(self):
        """测试无效的分析类型"""
        manager = SessionManager(session_timeout=30, max_concurrent_sessions=10)
        manager.start()
        
        try:
            # 尝试创建无效类型的会话
            with pytest.raises(ValueError, match="Unknown processor type"):
                manager.create_session("invalid_type")
            
            # 验证管理器状态未受影响
            assert manager.get_session_count() == 0
            
            # 验证仍可创建有效会话
            session_key = manager.create_session("dummy")
            assert session_key is not None
            assert manager.get_session_count() == 1
            
        finally:
            await manager.stop()
    
    @pytest.mark.asyncio
    async def test_session_timeout_edge_cases(self):
        """测试会话超时的边界情况"""
        manager = SessionManager(session_timeout=1, max_concurrent_sessions=10)
        manager.start()
        
        try:
            # 创建会话
            session_key = manager.create_session("dummy")
            session = manager.get_session(session_key)
            
            # 手动设置过期时间
            session.last_activity = datetime.now() - timedelta(seconds=2)
            
            # 验证会话被识别为过期
            assert session.is_expired(1)
            
            # 清理过期会话
            cleaned_count = manager.cleanup_expired_sessions()
            assert cleaned_count == 1
            assert manager.get_session_count() == 0
            
            # 尝试获取已清理的会话
            session = manager.get_session(session_key)
            assert session is None
            
        finally:
            await manager.stop()
    
    @pytest.mark.asyncio
    async def test_cleanup_with_active_processing(self):
        """测试清理有活跃处理的会话"""
        manager = SessionManager(session_timeout=30, max_concurrent_sessions=10)
        manager.start()
        
        try:
            # 创建会话并启动处理
            session_key = manager.create_session("dummy")
            session = manager.get_session(session_key)
            session.start_processing()
            
            # 验证处理任务正在运行
            assert session.processing_task is not None
            assert not session.processing_task.done()
            
            # 移除会话
            removed = manager.remove_session(session_key)
            assert removed is True
            
            # 等待一段时间确保清理完成
            await asyncio.sleep(0.1)
            
            # 验证处理任务被取消
            assert session.processing_task.cancelled() or session.processing_task.done()
            assert session.status == SessionStatus.STOPPED
            
        finally:
            await manager.stop()
    
    @pytest.mark.asyncio
    async def test_manager_stop_with_active_sessions(self):
        """测试停止管理器时有活跃会话"""
        manager = SessionManager(session_timeout=30, max_concurrent_sessions=10)
        manager.start()
        
        # 创建多个活跃会话
        session_keys = []
        for i in range(3):
            session_key = manager.create_session("dummy")
            session = manager.get_session(session_key)
            session.start_processing()
            session_keys.append(session_key)
        
        assert manager.get_session_count() == 3
        
        # 停止管理器
        await manager.stop()
        
        # 验证所有会话都被清理
        assert manager.get_session_count() == 0
        
        # 验证所有会话都无法获取
        for session_key in session_keys:
            session = manager.get_session(session_key)
            assert session is None


class TestProcessorErrorHandling:
    """测试处理器的错误处理"""
    
    def test_processor_initialization_failure(self):
        """测试处理器初始化失败"""
        # 创建一个会失败的处理器
        class FailingProcessor(VideoProcessor):
            def initialize(self):
                raise RuntimeError("Initialization failed")
            
            def process_frame(self, frame):
                return {}
            
            def cleanup(self):
                pass
        
        processor = FailingProcessor()
        
        # 初始化应该抛出异常
        with pytest.raises(RuntimeError, match="Initialization failed"):
            processor.initialize()
    
    def test_processor_frame_processing_failure(self):
        """测试处理器帧处理失败"""
        processor = DummyProcessor()
        processor.initialize()
        
        # 模拟处理失败
        original_method = processor.process_frame
        
        def failing_process_frame(frame):
            raise ValueError("Frame processing failed")
        
        processor.process_frame = failing_process_frame
        
        # 处理帧应该抛出异常
        frame = np.zeros((100, 100, 3), dtype=np.uint8)
        with pytest.raises(ValueError, match="Frame processing failed"):
            processor.process_frame(frame)
        
        # 恢复原方法验证处理器仍可工作
        processor.process_frame = original_method
        result = processor.process_frame(frame)
        assert isinstance(result, dict)
    
    def test_processor_cleanup_failure(self):
        """测试处理器清理失败"""
        # 创建一个清理会失败的处理器
        class FailingCleanupProcessor(VideoProcessor):
            def initialize(self):
                pass
            
            def process_frame(self, frame):
                return {"test": True}
            
            def cleanup(self):
                raise RuntimeError("Cleanup failed")
        
        processor = FailingCleanupProcessor()
        processor.initialize()
        
        # 清理应该抛出异常
        with pytest.raises(RuntimeError, match="Cleanup failed"):
            processor.cleanup()
    
    def test_opencv_processor_without_opencv(self):
        """测试OpenCV处理器在没有OpenCV时的行为"""
        processor = OpenCVPreviewProcessor()
        
        # 模拟OpenCV不可用
        with patch('processors.processors.cv2', side_effect=ImportError("No module named 'cv2'")):
            # 初始化不应该失败
            processor.initialize()
            assert processor._initialized
            assert processor._cv2 is None
            
            # 处理帧应该仍然工作（基本功能）
            frame = np.random.randint(0, 255, (100, 100, 3), dtype=np.uint8)
            result = processor.process_frame(frame)
            
            assert isinstance(result, dict)
            assert "dimensions" in result
            assert "frame_number" in result
            # 不应该有高级分析结果
            assert "statistics" not in result
            assert "histogram" not in result


class TestQueueErrorHandling:
    """测试队列的错误处理"""
    
    @pytest.mark.asyncio
    async def test_message_queue_with_invalid_results(self):
        """测试消息队列处理无效结果"""
        queue = SessionQueue(max_size=10)
        
        # 创建有效结果
        valid_result = ProcessingResult(
            session_key="test",
            timestamp=datetime.now(),
            frame_info={},
            analysis_data={"valid": True}
        )
        
        # 放入有效结果
        await queue.put_result(valid_result)
        
        # 获取结果
        result = await queue.get_result()
        assert result is not None
        assert result.analysis_data["valid"] is True
    
    @pytest.mark.asyncio
    async def test_frame_queue_with_invalid_frames(self):
        """测试帧队列处理无效帧"""
        queue = FrameQueue(max_size=10)
        
        # 放入有效帧
        valid_frame = np.zeros((100, 100, 3), dtype=np.uint8)
        queue.put_frame(valid_frame)
        
        # 放入None（应该被处理）
        queue.put_frame(None)
        
        # 获取帧
        frame1 = await queue.get_frame()
        frame2 = await queue.get_frame()
        
        assert np.array_equal(frame1, valid_frame)
        assert frame2 is None
    
    @pytest.mark.asyncio
    async def test_queue_operations_after_close(self):
        """测试关闭后的队列操作"""
        message_queue = SessionQueue(max_size=10)
        frame_queue = FrameQueue(max_size=10)
        
        # 关闭队列
        message_queue.close()
        frame_queue.close()
        
        # 尝试操作关闭的队列
        result = ProcessingResult(
            session_key="test",
            timestamp=datetime.now(),
            frame_info={},
            analysis_data={}
        )
        
        # 这些操作应该被忽略，不抛出异常
        await message_queue.put_result(result)
        frame = np.zeros((100, 100, 3), dtype=np.uint8)
        frame_queue.put_frame(frame)
        
        # 获取操作应该返回None
        retrieved_result = await message_queue.get_result()
        retrieved_frame = await frame_queue.get_frame()
        
        assert retrieved_result is None
        assert retrieved_frame is None


class TestVideoProcessingManagerErrorHandling:
    """测试VideoProcessingManager的错误处理"""
    
    @pytest.mark.asyncio
    async def test_stream_start_with_invalid_session(self):
        """测试无效会话的流开始"""
        session_manager = SessionManager()
        session_manager.start()
        
        try:
            manager = VideoProcessingManager(session_manager)
            
            # 尝试开始不存在的会话的流
            result = await manager.on_stream_start("nonexistent-session")
            assert result is False
            
        finally:
            await session_manager.stop()
    
    @pytest.mark.asyncio
    async def test_stream_start_with_stopped_session(self):
        """测试已停止会话的流开始"""
        session_manager = SessionManager()
        session_manager.start()
        
        try:
            manager = VideoProcessingManager(session_manager)
            
            # 创建会话并停止
            session_key = session_manager.create_session("dummy")
            session = session_manager.get_session(session_key)
            session.status = SessionStatus.STOPPED
            
            # 尝试开始已停止会话的流
            result = await manager.on_stream_start(session_key)
            assert result is False
            
        finally:
            await session_manager.stop()
    
    def test_video_decoder_with_invalid_payload(self):
        """测试视频解码器处理无效负载"""
        decoder = VideoDecoder()
        
        # 测试空负载
        frames = decoder.decode(b'')
        assert frames == []
        
        # 测试过短负载
        frames = decoder.decode(b'x')
        assert frames == []
        
        # 测试无效格式
        frames = decoder.decode(b'invalid_video_data')
        assert frames == []
    
    def test_video_decoder_error_recovery(self):
        """测试视频解码器错误恢复"""
        decoder = VideoDecoder()
        
        # 模拟解码错误
        with patch.object(decoder.codec_context, 'decode', side_effect=Exception("Decode error")):
            # 创建看起来有效的负载
            payload = b'\x17\x01\x00\x00\x00\x00\x00\x00\x01test'
            
            # 解码应该抛出异常
            with pytest.raises(Exception, match="Decode error"):
                decoder.decode(payload)


class TestSessionErrorHandling:
    """测试Session的错误处理"""
    
    @pytest.mark.asyncio
    async def test_session_processing_with_failing_processor(self):
        """测试处理器失败时的会话处理"""
        # 创建会失败的处理器
        class FailingProcessor(VideoProcessor):
            def __init__(self):
                self.call_count = 0
            
            def initialize(self):
                pass
            
            def process_frame(self, frame):
                self.call_count += 1
                if self.call_count == 2:
                    raise ValueError("Processing failed")
                return {"success": True, "call": self.call_count}
            
            def cleanup(self):
                pass
        
        # 创建会话
        processor = FailingProcessor()
        session = Session(
            session_key="test-session",
            analysis_type="test",
            processor=processor
        )
        
        # 启动处理
        session.start_processing()
        
        # 发送帧
        test_frame = np.zeros((100, 100, 3), dtype=np.uint8)
        session.frame_queue.put_frame(test_frame)  # 成功
        session.frame_queue.put_frame(test_frame)  # 失败
        session.frame_queue.put_frame(test_frame)  # 成功
        
        # 等待处理
        await asyncio.sleep(0.5)
        
        # 获取结果
        results = []
        for _ in range(3):
            result = await session.message_queue.get_result(timeout=1.0)
            if result:
                results.append(result)
        
        # 验证结果
        assert len(results) == 3
        
        # 检查错误结果
        success_results = [r for r in results if r.error is None]
        error_results = [r for r in results if r.error is not None]
        
        assert len(success_results) == 2
        assert len(error_results) == 1
        assert "Processing failed" in error_results[0].error
        
        # 停止处理
        session.stop_processing()
    
    @pytest.mark.asyncio
    async def test_session_cleanup_with_processor_error(self):
        """测试处理器清理错误时的会话清理"""
        # 创建清理会失败的处理器
        class FailingCleanupProcessor(VideoProcessor):
            def initialize(self):
                pass
            
            def process_frame(self, frame):
                return {"test": True}
            
            def cleanup(self):
                raise RuntimeError("Cleanup failed")
        
        # 创建会话
        processor = FailingCleanupProcessor()
        session = Session(
            session_key="test-session",
            analysis_type="test",
            processor=processor
        )
        
        # 启动处理
        session.start_processing()
        await asyncio.sleep(0.1)
        
        # 停止处理（应该处理清理错误）
        session.stop_processing()
        
        # 验证会话状态
        assert session.status == SessionStatus.STOPPED
        assert session.frame_queue.is_closed
        assert session.message_queue.is_closed
    
    @pytest.mark.asyncio
    async def test_session_processing_task_cancellation(self):
        """测试会话处理任务取消"""
        processor = DummyProcessor()
        session = Session(
            session_key="test-session",
            analysis_type="dummy",
            processor=processor
        )
        
        # 启动处理
        session.start_processing()
        
        # 验证任务正在运行
        assert session.processing_task is not None
        assert not session.processing_task.done()
        
        # 取消任务
        session.processing_task.cancel()
        
        # 等待取消完成
        try:
            await session.processing_task
        except asyncio.CancelledError:
            pass
        
        # 验证任务被取消
        assert session.processing_task.cancelled()


class TestIntegrationErrorHandling:
    """测试集成场景的错误处理"""
    
    @pytest.mark.asyncio
    async def test_full_system_error_recovery(self):
        """测试完整系统的错误恢复"""
        session_manager = SessionManager(session_timeout=30, max_concurrent_sessions=10)
        session_manager.start()
        
        try:
            # 创建正常会话
            session_key1 = session_manager.create_session("dummy")
            session1 = session_manager.get_session(session_key1)
            session1.start_processing()
            
            # 创建会出错的会话
            session_key2 = session_manager.create_session("dummy")
            session2 = session_manager.get_session(session_key2)
            
            # 模拟处理器错误
            original_process = session2.processor.process_frame
            def failing_process(frame):
                raise Exception("Simulated error")
            session2.processor.process_frame = failing_process
            
            session2.start_processing()
            
            # 发送帧到两个会话
            test_frame = np.zeros((100, 100, 3), dtype=np.uint8)
            session1.frame_queue.put_frame(test_frame)
            session2.frame_queue.put_frame(test_frame)
            
            # 等待处理
            await asyncio.sleep(0.5)
            
            # 获取结果
            result1 = await session1.message_queue.get_result(timeout=1.0)
            result2 = await session2.message_queue.get_result(timeout=1.0)
            
            # 验证正常会话仍然工作
            assert result1 is not None
            assert result1.error is None
            
            # 验证错误会话产生错误结果
            assert result2 is not None
            assert result2.error is not None
            
            # 验证系统整体仍然稳定
            assert session_manager.get_session_count() == 2
            
            # 可以继续创建新会话
            session_key3 = session_manager.create_session("dummy")
            assert session_key3 is not None
            
        finally:
            await session_manager.stop()


if __name__ == "__main__":
    pytest.main([__file__, "-v"])