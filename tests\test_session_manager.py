"""
测试会话管理系统
"""
import asyncio
import pytest
import pytest_asyncio
import numpy as np
from server.session_manager import Session<PERSON><PERSON><PERSON>, ProcessorFactory
from models import SessionStatus


@pytest_asyncio.fixture
async def session_manager():
    """创建会话管理器的fixture"""
    manager = SessionManager(session_timeout=10, max_concurrent_sessions=5)
    manager.start()
    yield manager
    await manager.stop()


@pytest.fixture
def test_frame():
    """创建测试帧数据的fixture"""
    return np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)


class TestSessionManager:
    """会话管理器测试类"""
    
    @pytest.mark.asyncio
    async def test_create_session(self, session_manager):
        """测试创建会话"""
        # 创建会话
        session_key1 = session_manager.create_session("dummy")
        session_key2 = session_manager.create_session("opencv_preview")
        
        # 验证会话创建成功
        assert session_key1 is not None
        assert session_key2 is not None
        assert session_key1 != session_key2
        assert session_manager.get_session_count() == 2
    
    @pytest.mark.asyncio
    async def test_get_session(self, session_manager):
        """测试获取会话"""
        # 创建会话
        session_key = session_manager.create_session("dummy")
        
        # 获取会话
        session = session_manager.get_session(session_key)
        
        # 验证会话信息
        assert session is not None
        assert session.session_key == session_key
        assert session.analysis_type == "dummy"
        assert session.status == SessionStatus.CREATED
    
    @pytest.mark.asyncio
    async def test_get_nonexistent_session(self, session_manager):
        """测试获取不存在的会话"""
        session = session_manager.get_session("nonexistent-key")
        assert session is None
    
    @pytest.mark.asyncio
    async def test_session_status(self, session_manager):
        """测试获取会话状态"""
        # 创建会话
        session_key = session_manager.create_session("dummy")
        
        # 获取状态
        status = session_manager.get_session_status(session_key)
        
        # 验证状态信息
        assert status is not None
        assert status["session_key"] == session_key
        assert status["analysis_type"] == "dummy"
        assert status["status"] == SessionStatus.CREATED.value
        assert "created_at" in status
        assert "last_activity" in status
        assert "processing_active" in status
    
    @pytest.mark.asyncio
    async def test_session_status_nonexistent(self, session_manager):
        """测试获取不存在会话的状态"""
        status = session_manager.get_session_status("nonexistent-key")
        assert status is None
    
    @pytest.mark.asyncio
    async def test_list_sessions(self, session_manager):
        """测试列出所有会话"""
        # 创建多个会话
        session_key1 = session_manager.create_session("dummy")
        session_key2 = session_manager.create_session("opencv_preview")
        
        # 列出会话
        sessions = session_manager.list_sessions()
        
        # 验证会话列表
        assert len(sessions) == 2
        session_keys = [s["session_key"] for s in sessions]
        assert session_key1 in session_keys
        assert session_key2 in session_keys
    
    @pytest.mark.asyncio
    async def test_remove_session(self, session_manager):
        """测试移除会话"""
        # 创建会话
        session_key = session_manager.create_session("dummy")
        assert session_manager.get_session_count() == 1
        
        # 移除会话
        removed = session_manager.remove_session(session_key)
        
        # 验证移除成功
        assert removed is True
        assert session_manager.get_session_count() == 0
        assert session_manager.get_session(session_key) is None
    
    @pytest.mark.asyncio
    async def test_remove_nonexistent_session(self, session_manager):
        """测试移除不存在的会话"""
        removed = session_manager.remove_session("nonexistent-key")
        assert removed is False
    
    @pytest.mark.asyncio
    async def test_max_concurrent_sessions(self):
        """测试最大并发会话数限制"""
        manager = SessionManager(session_timeout=10, max_concurrent_sessions=2)
        manager.start()
        
        try:
            # 创建最大数量的会话
            session_key1 = manager.create_session("dummy")
            session_key2 = manager.create_session("dummy")
            
            # 尝试创建超出限制的会话
            with pytest.raises(ValueError, match="Maximum concurrent sessions"):
                manager.create_session("dummy")
            
            assert manager.get_session_count() == 2
        finally:
            await manager.stop()
    
    @pytest.mark.asyncio
    async def test_session_timeout_cleanup(self):
        """测试会话超时清理"""
        manager = SessionManager(session_timeout=1, max_concurrent_sessions=5)
        manager.start()
        
        try:
            # 创建会话
            session_key = manager.create_session("dummy")
            assert manager.get_session_count() == 1
            
            # 等待超时
            await asyncio.sleep(2)
            
            # 手动触发清理
            cleaned_count = manager.cleanup_expired_sessions()
            
            # 验证清理结果
            assert cleaned_count == 1
            assert manager.get_session_count() == 0
        finally:
            await manager.stop()
    
    @pytest.mark.asyncio
    async def test_frame_processing(self, session_manager, test_frame):
        """测试帧处理功能"""
        # 创建会话
        session_key = session_manager.create_session("dummy")
        session = session_manager.get_session(session_key)
        
        # 启动处理
        session.start_processing()
        assert session.status == SessionStatus.PROCESSING
        
        # 发送帧数据
        session.frame_queue.put_frame(test_frame)
        
        # 等待处理
        await asyncio.sleep(0.5)
        
        # 获取结果
        result = await session.message_queue.get_result(timeout=2.0)
        
        # 验证结果
        assert result is not None
        assert result.frame_info is not None
        assert result.analysis_data is not None


class TestProcessorFactory:
    """处理器工厂测试类"""
    
    def test_get_available_types(self):
        """测试获取可用处理器类型"""
        available_types = ProcessorFactory.get_available_types()
        
        # 验证默认处理器类型存在
        assert "dummy" in available_types
        assert "opencv_preview" in available_types
        assert len(available_types) >= 2
    
    def test_create_processor(self):
        """测试创建处理器"""
        available_types = ProcessorFactory.get_available_types()
        
        for proc_type in available_types:
            processor = ProcessorFactory.create_processor(proc_type)
            assert processor is not None
            
            # 测试处理器基本功能
            processor.initialize()
            
            # 测试处理一帧
            test_frame = np.random.randint(0, 255, (100, 100, 3), dtype=np.uint8)
            result = processor.process_frame(test_frame)
            assert isinstance(result, dict)
            
            processor.cleanup()
    
    def test_create_unknown_processor(self):
        """测试创建未知类型的处理器"""
        with pytest.raises(ValueError, match="Unknown processor type"):
            ProcessorFactory.create_processor("unknown_type")


@pytest.mark.integration
class TestSessionManagerIntegration:
    """会话管理器集成测试"""
    
    @pytest.mark.asyncio
    async def test_full_workflow(self, test_frame):
        """测试完整的工作流程"""
        manager = SessionManager(session_timeout=30, max_concurrent_sessions=10)
        manager.start()
        
        try:
            # 1. 创建会话
            session_key = manager.create_session("dummy")
            assert session_key is not None
            
            # 2. 获取会话并启动处理
            session = manager.get_session(session_key)
            assert session is not None
            session.start_processing()
            
            # 3. 处理多帧数据
            results = []
            for i in range(3):
                session.frame_queue.put_frame(test_frame)
                await asyncio.sleep(0.1)
                
                result = await session.message_queue.get_result(timeout=2.0)
                if result:
                    results.append(result)
            
            # 4. 验证处理结果
            assert len(results) > 0
            for result in results:
                assert result.frame_info is not None
                assert result.analysis_data is not None
            
            # 5. 停止处理
            session.stop_processing()
            
            # 6. 移除会话
            removed = manager.remove_session(session_key)
            assert removed is True
            assert manager.get_session_count() == 0
            
        finally:
            await manager.stop()