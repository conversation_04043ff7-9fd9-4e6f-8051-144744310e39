"""
测试消息队列系统
"""
import asyncio
import pytest
from datetime import datetime
from ..models import SessionQueue, ProcessingResult


class TestSessionQueue:
    """测试SessionQueue类"""
    
    @pytest.mark.asyncio
    async def test_queue_creation(self):
        """测试队列创建"""
        queue = SessionQueue(max_size=10)
        assert not queue.is_closed
        assert queue._queue.maxsize == 10
    
    @pytest.mark.asyncio
    async def test_put_and_get_result(self):
        """测试放入和获取结果"""
        queue = SessionQueue(max_size=10)
        
        # 创建测试结果
        result = ProcessingResult(
            session_key="test-session",
            timestamp=datetime.now(),
            frame_info={"width": 640, "height": 480},
            analysis_data={"test": "data"}
        )
        
        # 放入结果
        await queue.put_result(result)
        
        # 获取结果
        retrieved_result = await queue.get_result()
        
        assert retrieved_result is not None
        assert retrieved_result.session_key == "test-session"
        assert retrieved_result.frame_info["width"] == 640
        assert retrieved_result.analysis_data["test"] == "data"
    
    @pytest.mark.asyncio
    async def test_get_result_with_timeout(self):
        """测试带超时的获取结果"""
        queue = SessionQueue(max_size=10)
        
        # 测试超时获取
        result = await queue.get_result(timeout=0.1)
        assert result is None
    
    @pytest.mark.asyncio
    async def test_queue_full_behavior(self):
        """测试队列满时的行为"""
        queue = SessionQueue(max_size=2)
        
        # 创建测试结果
        result1 = ProcessingResult(
            session_key="test-1",
            timestamp=datetime.now(),
            frame_info={},
            analysis_data={"order": 1}
        )
        result2 = ProcessingResult(
            session_key="test-2",
            timestamp=datetime.now(),
            frame_info={},
            analysis_data={"order": 2}
        )
        result3 = ProcessingResult(
            session_key="test-3",
            timestamp=datetime.now(),
            frame_info={},
            analysis_data={"order": 3}
        )
        
        # 放入结果直到队列满
        await queue.put_result(result1)
        await queue.put_result(result2)
        await queue.put_result(result3)  # 这应该会丢弃最旧的结果
        
        # 获取结果，应该是result2和result3
        retrieved1 = await queue.get_result()
        retrieved2 = await queue.get_result()
        
        # 验证最旧的结果被丢弃了
        assert retrieved1.analysis_data["order"] == 2
        assert retrieved2.analysis_data["order"] == 3
    
    @pytest.mark.asyncio
    async def test_queue_close(self):
        """测试队列关闭"""
        queue = SessionQueue(max_size=10)
        
        # 关闭队列
        queue.close()
        assert queue.is_closed
        
        # 关闭后不能放入结果
        result = ProcessingResult(
            session_key="test",
            timestamp=datetime.now(),
            frame_info={},
            analysis_data={}
        )
        await queue.put_result(result)  # 应该被忽略
        
        # 关闭后获取结果返回None
        retrieved_result = await queue.get_result()
        assert retrieved_result is None
    
    @pytest.mark.asyncio
    async def test_concurrent_operations(self):
        """测试并发操作"""
        queue = SessionQueue(max_size=100)
        results = []
        
        async def producer():
            """生产者协程"""
            for i in range(10):
                result = ProcessingResult(
                    session_key=f"session-{i}",
                    timestamp=datetime.now(),
                    frame_info={},
                    analysis_data={"index": i}
                )
                await queue.put_result(result)
                await asyncio.sleep(0.01)  # 小延迟模拟实际情况
        
        async def consumer():
            """消费者协程"""
            for _ in range(10):
                result = await queue.get_result(timeout=1.0)
                if result:
                    results.append(result)
        
        # 并发运行生产者和消费者
        await asyncio.gather(producer(), consumer())
        
        # 验证所有结果都被正确处理
        assert len(results) == 10
        indices = [r.analysis_data["index"] for r in results]
        assert set(indices) == set(range(10))
    
    @pytest.mark.asyncio
    async def test_memory_management(self):
        """测试内存管理"""
        queue = SessionQueue(max_size=5)
        
        # 创建大量结果来测试内存管理
        for i in range(20):
            result = ProcessingResult(
                session_key=f"session-{i}",
                timestamp=datetime.now(),
                frame_info={"large_data": "x" * 1000},  # 模拟大数据
                analysis_data={"index": i}
            )
            await queue.put_result(result)
        
        # 队列应该只保留最新的5个结果
        retrieved_results = []
        while True:
            result = await queue.get_result(timeout=0.1)
            if result is None:
                break
            retrieved_results.append(result)
        
        # 验证只有最新的5个结果
        assert len(retrieved_results) == 5
        indices = [r.analysis_data["index"] for r in retrieved_results]
        assert indices == list(range(15, 20))


if __name__ == "__main__":
    # 运行测试
    asyncio.run(pytest.main([__file__, "-v"]))