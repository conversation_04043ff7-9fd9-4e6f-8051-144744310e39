# 集成测试文档

本文档描述了RTMP视频处理API的集成测试套件，包括端到端测试、FFmpeg模拟RTMP流输入测试、错误场景测试和并发会话处理能力测试。

## 测试概述

集成测试套件包含以下主要测试类别：

1. **端到端集成测试** (`TestEndToEndIntegration`)
   - 完整的会话创建到结果接收流程
   - RTMP连接验证
   - SSE事件流测试
   - 并发会话处理

2. **错误场景测试** (`TestErrorScenarios`)
   - RTMP连接中断和恢复
   - 处理器错误处理
   - 会话超时处理
   - 最大并发会话限制

3. **性能和稳定性测试** (`TestPerformanceAndStability`)
   - 长时间运行会话
   - 快速会话创建和删除
   - 内存泄漏检测

4. **FFmpeg集成测试** (`TestFFmpegIntegration`)
   - 不同视频格式处理
   - 真实RTMP流模拟

5. **高级集成测试** (`TestStressAndPerformance`, `TestErrorRecovery`, `TestBoundaryConditions`)
   - 高并发压力测试
   - 系统恢复能力测试
   - 边界条件测试

## 文件结构

```
tests/
├── test_integration.py              # 基础集成测试
├── test_integration_advanced.py     # 高级集成测试
├── test_config.toml                 # 测试专用配置
├── run_integration_tests.py         # 测试运行脚本
├── test_runner.py                   # 简单测试验证器
└── README_INTEGRATION_TESTS.md      # 本文档
```

## 依赖要求

### 必需依赖
- Python 3.8+
- pytest
- httpx
- numpy
- psutil

### 可选依赖
- FFmpeg (用于真实RTMP流测试)

### 安装依赖

```bash
# 安装Python依赖
pip install pytest httpx numpy psutil

# 安装FFmpeg (Ubuntu/Debian)
sudo apt-get install ffmpeg

# 安装FFmpeg (macOS)
brew install ffmpeg

# 安装FFmpeg (Windows)
# 下载并安装从 https://ffmpeg.org/download.html
```

## 运行测试

### 1. 使用测试运行脚本（推荐）

```bash
# 检查依赖
python tests/run_integration_tests.py --check-deps

# 运行基础集成测试
python tests/run_integration_tests.py --type basic

# 运行完整集成测试
python tests/run_integration_tests.py --type full

# 运行FFmpeg相关测试
python tests/run_integration_tests.py --type ffmpeg

# 详细输出
python tests/run_integration_tests.py --type basic --verbose
```

### 2. 直接使用pytest

```bash
# 运行所有集成测试
pytest tests/test_integration.py -m integration -v

# 运行基础测试（排除慢速测试）
pytest tests/test_integration.py -m "integration and not slow" -v

# 运行高级测试
pytest tests/test_integration_advanced.py -m integration -v

# 运行特定测试类
pytest tests/test_integration.py::TestEndToEndIntegration -v
```

### 3. 验证基本功能

```bash
# 运行简单的功能验证
python tests/test_runner.py
```

## 测试配置

测试使用独立的配置文件 `tests/test_config.toml`，主要差异：

- RTMP端口: 1936 (避免与主应用冲突)
- FastAPI端口: 8001 (避免与主应用冲突)
- 较小的并发会话数和队列大小
- 较短的超时时间
- 减少的日志输出

## 测试标记

测试使用pytest标记进行分类：

- `@pytest.mark.integration`: 所有集成测试
- `@pytest.mark.slow`: 耗时较长的测试
- `@pytest.mark.skipif`: 条件跳过（如FFmpeg不可用）

## 主要测试场景

### 1. 端到端流程测试

```python
async def test_complete_session_flow():
    # 1. 创建会话
    # 2. 建立SSE连接
    # 3. 开始RTMP推流
    # 4. 验证处理结果
    # 5. 清理资源
```

### 2. 并发会话测试

```python
async def test_concurrent_sessions():
    # 创建多个会话
    # 同时建立SSE连接
    # 并发RTMP推流
    # 验证所有会话都正常工作
```

### 3. 错误恢复测试

```python
async def test_rtmp_connection_interruption():
    # 开始推流
    # 中断连接
    # 重新连接
    # 验证系统恢复
```

### 4. 性能测试

```python
async def test_high_concurrency_sessions():
    # 创建大量并发会话
    # 监控系统资源使用
    # 验证性能指标
```

## FFmpeg测试工具

### FFmpegRTMPStreamer类

用于模拟真实的RTMP流输入：

```python
streamer = FFmpegRTMPStreamer(
    rtmp_url="rtmp://localhost:1936/live/session_key",
    duration=10,  # 流持续时间（秒）
    fps=5         # 帧率
)

streamer.start_streaming()  # 开始推流
# ... 测试逻辑 ...
streamer.stop_streaming()   # 停止推流
```

### SSEClient类

用于接收服务器推送的事件：

```python
sse_client = SSEClient("http://localhost:8001/events/session_key")
sse_client.start()

# 等待事件
sse_client.wait_for_events(count=5, timeout=10.0)

# 获取特定类型的事件
results = sse_client.get_events_by_type("result")

sse_client.stop()
```

## 测试处理器

### TestVideoProcessor

基础测试处理器，记录处理的帧信息：

```python
class TestVideoProcessor(VideoProcessor):
    def process_frame(self, frame: np.ndarray) -> dict:
        return {
            "frame_shape": frame.shape,
            "frame_number": self.process_count,
            "processed": True,
            "timestamp": time.time()
        }
```

### StressTestProcessor

压力测试处理器，支持配置处理延迟和错误率：

```python
processor = StressTestProcessor(
    processing_delay=0.1,  # 处理延迟
    error_rate=0.1         # 10%错误率
)
```

## 故障排除

### 常见问题

1. **FFmpeg不可用**
   ```
   RuntimeError: FFmpeg not found. Please install FFmpeg to run integration tests.
   ```
   解决：安装FFmpeg并确保在PATH中

2. **端口冲突**
   ```
   OSError: [Errno 98] Address already in use
   ```
   解决：检查测试配置中的端口设置，确保没有其他服务占用

3. **测试超时**
   ```
   asyncio.TimeoutError
   ```
   解决：增加测试超时时间或检查系统性能

4. **内存不足**
   ```
   MemoryError
   ```
   解决：减少并发测试数量或增加系统内存

### 调试技巧

1. **启用详细日志**
   ```bash
   pytest tests/test_integration.py -v -s --log-cli-level=DEBUG
   ```

2. **运行单个测试**
   ```bash
   pytest tests/test_integration.py::TestEndToEndIntegration::test_complete_session_flow -v
   ```

3. **跳过慢速测试**
   ```bash
   pytest tests/test_integration.py -m "integration and not slow" -v
   ```

4. **查看测试覆盖率**
   ```bash
   pytest tests/test_integration.py --cov=server --cov-report=html
   ```

## 性能基准

### 预期性能指标

- **单会话处理能力**: > 10 FPS
- **并发会话数**: 5-10个会话
- **内存使用**: < 100MB增长
- **响应延迟**: < 1秒

### 监控指标

- CPU使用率
- 内存使用量
- 网络带宽
- 处理延迟
- 错误率

## 持续集成

### GitHub Actions示例

```yaml
name: Integration Tests

on: [push, pull_request]

jobs:
  integration-tests:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v2
    
    - name: Set up Python
      uses: actions/setup-python@v2
      with:
        python-version: 3.8
    
    - name: Install FFmpeg
      run: sudo apt-get install ffmpeg
    
    - name: Install dependencies
      run: |
        pip install -r requirements.txt
        pip install pytest httpx psutil
    
    - name: Run integration tests
      run: python tests/run_integration_tests.py --type basic
```

## 贡献指南

### 添加新测试

1. 在适当的测试类中添加测试方法
2. 使用适当的pytest标记
3. 包含清理代码
4. 添加文档说明

### 测试命名规范

- 测试方法: `test_<功能描述>`
- 测试类: `Test<功能模块>`
- 测试文件: `test_<模块名>.py`

### 代码风格

- 遵循PEP 8
- 使用类型提示
- 添加适当的注释
- 包含错误处理

## 参考资料

- [pytest文档](https://docs.pytest.org/)
- [httpx文档](https://www.python-httpx.org/)
- [FFmpeg文档](https://ffmpeg.org/documentation.html)
- [asyncio文档](https://docs.python.org/3/library/asyncio.html)