#!/usr/bin/env python3
"""
测试OpenCV摄像头捕获和FFmpeg推流功能
"""

import cv2
import subprocess
import time
import threading
import sys

def test_opencv_camera():
    """测试OpenCV摄像头功能"""
    print("=" * 50)
    print("测试OpenCV摄像头功能")
    print("=" * 50)
    
    try:
        # 初始化摄像头
        cap = cv2.VideoCapture(0)
        if not cap.isOpened():
            print("✗ 无法打开摄像头")
            return False
        
        # 获取摄像头参数
        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        fps = cap.get(cv2.CAP_PROP_FPS)
        
        print(f"✓ 摄像头参数: {width}x{height} @ {fps}fps")
        
        # 测试读取几帧
        for i in range(5):
            ret, frame = cap.read()
            if not ret:
                print(f"✗ 无法读取第{i+1}帧")
                cap.release()
                return False
            print(f"✓ 成功读取第{i+1}帧: {frame.shape}")
        
        cap.release()
        print("✓ OpenCV摄像头测试通过")
        return True
        
    except Exception as e:
        print(f"✗ OpenCV摄像头测试失败: {e}")
        return False


def test_ffmpeg_rawvideo():
    """测试FFmpeg原始视频处理"""
    print("\n" + "=" * 50)
    print("测试FFmpeg原始视频处理")
    print("=" * 50)
    
    try:
        # 创建测试视频数据
        width, height = 640, 480
        frame_size = width * height * 3  # BGR24格式
        
        # 创建一个简单的测试帧（蓝色背景）
        import numpy as np
        test_frame = np.zeros((height, width, 3), dtype=np.uint8)
        test_frame[:, :, 0] = 255  # 蓝色通道
        
        print(f"创建测试帧: {test_frame.shape}, 大小: {test_frame.nbytes} 字节")
        
        # 测试FFmpeg命令（输出到null，不实际推流）
        ffmpeg_cmd = [
            "ffmpeg",
            "-y",
            "-f", "rawvideo",
            "-vcodec", "rawvideo", 
            "-pix_fmt", "bgr24",
            "-s", f"{width}x{height}",
            "-r", "30",
            "-i", "-",
            "-c:v", "libx264",
            "-preset", "ultrafast",
            "-t", "2",  # 只处理2秒
            "-f", "null",
            "-"
        ]
        
        print("启动FFmpeg测试进程...")
        process = subprocess.Popen(
            ffmpeg_cmd,
            stdin=subprocess.PIPE,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
        )
        
        # 发送几帧测试数据
        for i in range(60):  # 2秒 * 30fps = 60帧
            try:
                process.stdin.write(test_frame.tobytes())
                process.stdin.flush()
            except BrokenPipeError:
                break
        
        # 关闭输入并等待完成
        process.stdin.close()
        stdout, stderr = process.communicate(timeout=10)
        
        if process.returncode == 0:
            print("✓ FFmpeg原始视频处理测试通过")
            return True
        else:
            print(f"✗ FFmpeg测试失败 (返回码: {process.returncode})")
            print(f"错误信息: {stderr.decode('utf-8', errors='ignore')}")
            return False
            
    except subprocess.TimeoutExpired:
        print("✗ FFmpeg测试超时")
        process.kill()
        return False
    except Exception as e:
        print(f"✗ FFmpeg测试失败: {e}")
        return False


def test_opencv_ffmpeg_integration():
    """测试OpenCV和FFmpeg集成"""
    print("\n" + "=" * 50)
    print("测试OpenCV和FFmpeg集成")
    print("=" * 50)
    
    cap = None
    process = None
    
    try:
        # 初始化摄像头
        cap = cv2.VideoCapture(0)
        if not cap.isOpened():
            print("✗ 无法打开摄像头")
            return False
        
        # 获取摄像头参数
        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        
        print(f"摄像头分辨率: {width}x{height}")
        
        # 启动FFmpeg进程（输出到null）
        ffmpeg_cmd = [
            "ffmpeg",
            "-y",
            "-f", "rawvideo",
            "-vcodec", "rawvideo",
            "-pix_fmt", "bgr24", 
            "-s", f"{width}x{height}",
            "-r", "30",
            "-i", "-",
            "-c:v", "libx264",
            "-preset", "ultrafast",
            "-t", "3",  # 只处理3秒
            "-f", "null",
            "-"
        ]
        
        print("启动FFmpeg进程...")
        process = subprocess.Popen(
            ffmpeg_cmd,
            stdin=subprocess.PIPE,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
        )
        
        # 捕获并发送帧
        frame_count = 0
        start_time = time.time()
        
        print("开始捕获和发送帧...")
        while time.time() - start_time < 3:  # 运行3秒
            ret, frame = cap.read()
            if not ret:
                print("无法读取摄像头帧")
                break
            
            try:
                process.stdin.write(frame.tobytes())
                process.stdin.flush()
                frame_count += 1
                
                if frame_count % 30 == 0:  # 每秒显示一次
                    print(f"已发送 {frame_count} 帧")
                    
            except BrokenPipeError:
                print("FFmpeg进程已终止")
                break
        
        # 关闭输入
        process.stdin.close()
        
        # 等待FFmpeg完成
        stdout, stderr = process.communicate(timeout=5)
        
        print(f"总共发送 {frame_count} 帧")
        
        if process.returncode == 0:
            print("✓ OpenCV和FFmpeg集成测试通过")
            return True
        else:
            print(f"✗ 集成测试失败 (返回码: {process.returncode})")
            return False
            
    except Exception as e:
        print(f"✗ 集成测试失败: {e}")
        return False
        
    finally:
        if cap:
            cap.release()
        if process:
            try:
                process.kill()
                process.wait()
            except:
                pass


def main():
    """主测试函数"""
    print("OpenCV + FFmpeg 推流功能测试")
    print("此程序将测试OpenCV摄像头捕获和FFmpeg推流的各项功能")
    
    # 检查依赖
    try:
        import cv2
        import numpy as np
        print("✓ 依赖库检查通过")
    except ImportError as e:
        print(f"✗ 缺少依赖库: {e}")
        return False
    
    # 运行测试
    tests = [
        ("OpenCV摄像头", test_opencv_camera),
        ("FFmpeg原始视频", test_ffmpeg_rawvideo), 
        ("OpenCV+FFmpeg集成", test_opencv_ffmpeg_integration)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n开始测试: {test_name}")
        result = test_func()
        results.append((test_name, result))
    
    # 显示测试结果
    print("\n" + "=" * 50)
    print("测试结果汇总")
    print("=" * 50)
    
    all_passed = True
    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name}: {status}")
        if not result:
            all_passed = False
    
    if all_passed:
        print("\n🎉 所有测试通过！可以使用OpenCV推流功能了")
    else:
        print("\n❌ 部分测试失败，请检查上述错误信息")
    
    return all_passed


if __name__ == "__main__":
    main()
