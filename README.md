# RTMP视频处理API

一个基于RTMP的实时视频流处理系统，通过FastAPI提供REST API和Server-Sent Events (SSE)，实现客户端视频流的实时处理和结果返回。

## 系统架构

### 核心组件

```
┌─────────────────┐    RTMP Stream     ┌─────────────────┐
│   Client App    │ ──────────────────→ │   RTMP Server   │
│                 │                     │                 │
└─────────────────┘                     └─────────────────┘
         │                                       │
         │ REST API                              │ Frame Data
         │                                       ▼
         ▼                               ┌─────────────────┐
┌─────────────────┐                     │ Video Processor │
│  FastAPI Server │                     │    Manager      │
│                 │                     └─────────────────┘
└─────────────────┘                             │
         │                                      │ Results
         │ SSE Connection                       ▼
         │                               ┌─────────────────┐
         └──────────────────────────────→│ Message Queue   │
                                         │   (per session) │
                                         └─────────────────┘
```

### 工作流程

1. **会话创建**: 客户端通过REST API创建处理会话，指定处理器类型
2. **SSE连接**: 客户端建立SSE连接接收实时处理结果
3. **RTMP推流**: 客户端使用会话密钥作为流密钥推送RTMP视频流
4. **视频处理**: 系统接收视频帧，调用指定处理器进行分析
5. **结果推送**: 处理结果通过SSE实时推送给客户端

## 功能特性

- ✅ **会话管理**: 支持多个并发视频处理会话
- ✅ **实时处理**: 低延迟的视频帧处理和结果推送
- ✅ **可扩展处理器**: 支持多种视频处理器的动态注册
- ✅ **异步架构**: 基于asyncio的高性能异步处理
- ✅ **资源管理**: 自动会话超时和资源清理
- ✅ **错误处理**: 完善的错误处理和日志记录
- ✅ **配置管理**: 支持TOML配置文件和环境变量

## API接口

### REST API

#### 创建会话
```http
POST /api/session
Content-Type: application/json

{
    "analysis_type": "opencv_preview"
}
```

**响应:**
```json
{
    "session_key": "uuid-string",
    "analysis_type": "opencv_preview",
    "created_at": "2024-01-01T12:00:00"
}
```

#### 获取会话状态
```http
GET /api/session/{session_key}
```

**响应:**
```json
{
    "session_key": "uuid-string",
    "analysis_type": "opencv_preview",
    "status": "active",
    "created_at": "2024-01-01T12:00:00",
    "last_activity": "2024-01-01T12:01:00",
    "processing_active": true
}
```

#### 删除会话
```http
DELETE /api/session/{session_key}
```

#### 获取可用处理器类型
```http
GET /api/processors
```

**响应:**
```json
{
    "available_types": ["opencv_preview", "dummy"]
}
```

#### 列出所有会话
```http
GET /api/sessions
```

### SSE事件流

#### 建立SSE连接
```http
GET /events/{session_key}
```

**事件类型:**
- `connected`: 连接建立确认
- `result`: 处理结果数据
- `heartbeat`: 心跳保持连接
- `error`: 错误信息
- `close`: 连接关闭

**结果事件示例:**
```json
{
    "session_key": "uuid-string",
    "timestamp": "2024-01-01T12:00:00",
    "frame_info": {
        "frame_number": 123
    },
    "analysis_data": {
        "frame_number": 123,
        "dimensions": {
            "width": 640,
            "height": 480,
            "channels": 3
        },
        "statistics": {
            "mean": 128.5,
            "std": 45.2,
            "min": 0,
            "max": 255
        }
    }
}
```

### RTMP推流

使用会话密钥作为流密钥推送RTMP流:
```
rtmp://localhost:1935/{session_key}
```

## 安装和部署

### 系统要求

- Python 3.8+
- FFmpeg (用于RTMP推流测试)
- OpenCV (可选，用于OpenCVPreviewProcessor)

### 安装依赖

```bash
# 安装Python依赖
pip install -r requirements.txt

# 安装OpenCV (可选)
pip install opencv-python

# 安装FFmpeg
# Windows: 下载并添加到PATH
# Ubuntu: sudo apt install ffmpeg
# macOS: brew install ffmpeg
```

### 配置文件

创建 `config.toml` 配置文件:

```toml
[rtmp]
host = "0.0.0.0"
port = 1935

[fastapi]
host = "0.0.0.0"
port = 8000
log_level = "info"
cors_origins = ["*"]

[session]
timeout = 3600              # 会话超时时间（秒）
max_concurrent_sessions = 100
cleanup_interval = 300

[queue]
max_size = 1000
timeout = 1.0
frame_queue_size = 100

[processing]
max_frame_rate = 30
frame_skip_threshold = 5
processing_timeout = 5.0

[logging]
level = "INFO"
file_path = "logs/app.log"
max_file_size = 10485760    # 10MB
backup_count = 5
```

### 环境变量配置

也可以使用环境变量覆盖配置:

```bash
export APP_RTMP_HOST=0.0.0.0
export APP_RTMP_PORT=1935
export APP_FASTAPI_HOST=0.0.0.0
export APP_FASTAPI_PORT=8000
export APP_SESSION_TIMEOUT=3600
export APP_LOGGING_LEVEL=INFO
export APP_LOGGING_FILE_PATH=logs/app.log
```

### 启动服务

```bash
# 启动完整服务
python main.py

# 或者单独启动API服务器（用于开发）
python -m server.api_server
```

服务启动后:
- RTMP服务器: `rtmp://localhost:1935`
- API服务器: `http://localhost:8000`
- API文档: `http://localhost:8000/docs`

## 使用示例

### Python客户端示例

运行完整的客户端示例:

```bash
# 安装客户端依赖
pip install httpx requests sseclient-py

# 运行示例
python example_client.py
```

示例功能:
- 创建OpenCVPreviewProcessor会话
- 建立SSE连接接收结果
- 使用FFmpeg推送测试视频流
- 计算端到端延迟
- 显示处理结果统计

### 手动测试

#### 1. 创建会话
```bash
curl -X POST http://localhost:8000/api/session \
  -H "Content-Type: application/json" \
  -d '{"analysis_type": "opencv_preview"}'
```

#### 2. 建立SSE连接
```bash
curl -N http://localhost:8000/events/{session_key}
```

#### 3. 推送RTMP流
```bash
# 使用测试源
ffmpeg -f lavfi -i testsrc=duration=30:size=640x480:rate=30 \
  -c:v libx264 -preset ultrafast -tune zerolatency \
  -f flv rtmp://localhost:1935/live/{session_key}

# 使用摄像头 (Windows)
ffmpeg -f dshow -i "video=Integrated Camera" \
  -c:v libx264 -preset ultrafast -tune zerolatency \
  -f flv rtmp://localhost:1935/live/{session_key}

# 使用视频文件
ffmpeg -re -i input.mp4 \
  -c:v libx264 -preset ultrafast -tune zerolatency \
  -f flv rtmp://localhost:1935/live/{session_key}
```

## 视频处理器

### 内置处理器

#### OpenCVPreviewProcessor
- **类型**: `opencv_preview`
- **功能**: 基于OpenCV的视频帧分析
- **输出**: 帧尺寸、统计信息、直方图数据
- **依赖**: opencv-python

#### DummyProcessor
- **类型**: `dummy`
- **功能**: 简单的测试处理器
- **输出**: 基本帧信息
- **依赖**: 无

### 自定义处理器

实现 `VideoProcessor` 接口创建自定义处理器:

```python
from models import VideoProcessor
import numpy as np
from typing import Dict, Any

class CustomProcessor(VideoProcessor):
    def initialize(self) -> None:
        """初始化处理器资源"""
        pass
    
    def process_frame(self, frame: np.ndarray) -> Dict[str, Any]:
        """处理视频帧"""
        # 实现自定义处理逻辑
        return {
            "custom_data": "processed_result"
        }
    
    def cleanup(self) -> None:
        """清理处理器资源"""
        pass

# 注册处理器
from server.session_manager import ProcessorFactory
ProcessorFactory.register_processor("custom", CustomProcessor)
```

## 性能优化

### 延迟优化
- 使用 `ultrafast` 编码预设
- 启用 `zerolatency` 调优
- 减小队列大小限制
- 优化处理器算法

### 并发处理
- 每个会话独立的处理协程
- 异步队列操作
- 非阻塞帧处理

### 资源管理
- 自动会话超时清理
- 队列大小限制
- 内存使用监控

## 监控和调试

### 日志系统

系统使用结构化日志记录:

```json
{
    "timestamp": "2024-01-01T12:00:00",
    "level": "INFO",
    "logger": "session_manager",
    "message": "Session created",
    "session_id": "uuid-string",
    "operation": "create_session",
    "extra_data": {
        "analysis_type": "opencv_preview"
    }
}
```

### 健康检查

```bash
curl http://localhost:8000/health
```

### 会话监控

```bash
# 列出所有会话
curl http://localhost:8000/api/sessions

# 获取特定会话状态
curl http://localhost:8000/api/session/{session_key}
```

## 错误处理

### 常见错误

#### 会话不存在 (404)
```json
{
    "detail": "Session not found: {session_key}"
}
```

#### 不支持的处理器类型 (400)
```json
{
    "detail": "Unsupported analysis_type: invalid_type, available types: ['opencv_preview', 'dummy']"
}
```

#### 达到最大会话数 (400)
```json
{
    "detail": "Maximum concurrent sessions reached"
}
```

### 故障排除

#### RTMP连接失败
1. 检查会话是否存在
2. 确认RTMP服务器正在运行
3. 验证流密钥格式

#### SSE连接中断
1. 检查网络连接
2. 验证会话状态
3. 查看服务器日志

#### 处理器错误
1. 检查依赖库安装
2. 验证处理器初始化
3. 查看错误日志

## 开发指南

### 项目结构

```
├── main.py                 # 主应用入口
├── config.py              # 配置管理
├── config.toml            # 配置文件
├── logging_config.py      # 日志配置
├── models.py              # 数据模型
├── example_client.py      # 客户端示例
├── requirements.txt       # Python依赖
├── server/
│   ├── api_server.py      # FastAPI服务器
│   ├── rtmp_server.py     # RTMP服务器
│   ├── session_manager.py # 会话管理
│   └── video_processing_manager.py # 视频处理管理
├── processors/
│   └── processors.py      # 视频处理器
├── tests/                 # 测试文件
└── logs/                  # 日志目录
```

### 运行测试

```bash
# 运行所有测试
python -m pytest

# 运行单元测试
python -m pytest tests/test_*.py -v

# 运行集成测试
python tests/run_integration_tests.py
```

### 代码风格

项目使用以下代码规范:
- PEP 8 Python代码风格
- Type hints for better code documentation
- Docstrings for all public methods
- Structured logging with context

## 许可证

本项目采用 MIT 许可证。详见 LICENSE 文件。

## 贡献

欢迎提交 Issue 和 Pull Request！

### 开发环境设置

```bash
# 克隆项目
git clone <repository-url>
cd rtmp-video-processing-api

# 创建虚拟环境
python -m venv .venv
source .venv/bin/activate  # Linux/macOS
# 或
.venv\Scripts\activate     # Windows

# 安装依赖
pip install -r requirements.txt

# 运行测试
python -m pytest
```

### 提交规范

- 使用清晰的提交信息
- 包含相关测试
- 更新文档
- 遵循代码风格

## 更新日志

### v1.0.0
- 初始版本发布
- 支持RTMP视频流处理
- FastAPI REST接口
- SSE实时结果推送
- 会话管理系统
- OpenCV和Dummy处理器
- 完整的配置管理
- 结构化日志系统