"""
核心数据模型和接口定义
"""
from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
from typing import Dict, Any, Optional
import asyncio
import numpy as np

from logging_config import get_logger, get_error_handler

# 初始化日志记录器
logger = get_logger(__name__)
queue_error_handler = get_error_handler("queue")


class SessionStatus(Enum):
    """会话状态枚举"""
    CREATED = "created"
    ACTIVE = "active"
    PROCESSING = "processing"
    STOPPED = "stopped"
    ERROR = "error"


@dataclass
class ProcessingResult:
    """处理结果数据模型"""
    session_key: str
    timestamp: datetime
    frame_info: Dict[str, Any]  # 帧尺寸、格式等
    analysis_data: Dict[str, Any]  # 具体分析结果
    error: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式，便于JSON序列化"""
        return {
            "session_key": self.session_key,
            "timestamp": self.timestamp.isoformat(),
            "frame_info": self.frame_info,
            "analysis_data": self.analysis_data,
            "error": self.error
        }


class VideoProcessor(ABC):
    """视频处理器抽象基类"""
    
    @abstractmethod
    def initialize(self) -> None:
        """初始化处理器资源"""
        pass
    
    @abstractmethod
    def process_frame(self, frame: np.ndarray) -> Dict[str, Any]:
        """
        同步阻塞处理单帧，返回分析结果
        
        Args:
            frame: 视频帧数据 (numpy数组)
            
        Returns:
            Dict[str, Any]: 分析结果字典
        """
        pass
    
    @abstractmethod
    def cleanup(self) -> None:
        """清理处理器资源"""
        pass


class SessionQueue:
    """会话消息队列"""
    
    def __init__(self, max_size: int = 1000):
        self._queue = asyncio.Queue(maxsize=max_size)
        self._closed = False
        self._logger = get_logger(f"{__name__}.SessionQueue")
    
    async def put_result(self, result: ProcessingResult) -> None:
        """放入处理结果"""
        if not self._closed:
            try:
                # 尝试非阻塞放入
                self._queue.put_nowait(result)
                self._logger.debug(f"Put result into queue for session {result.session_key}")
            except asyncio.QueueFull:
                # 队列满时丢弃最旧的结果
                self._logger.warning(f"Session queue full for session {result.session_key}, dropping oldest result")
                try:
                    self._queue.get_nowait()
                    self._queue.put_nowait(result)
                except asyncio.QueueEmpty:
                    # 如果队列为空但仍然满，直接忽略
                    pass
    
    async def get_result(self, timeout: float = None) -> Optional[ProcessingResult]:
        """获取处理结果"""
        if self._closed:
            return None
        
        try:
            if timeout is None:
                result = await self._queue.get()
                self._logger.debug("Got result from queue")
                return result
            else:
                result = await asyncio.wait_for(self._queue.get(), timeout=timeout)
                self._logger.debug("Got result from queue with timeout")
                return result
        except (asyncio.TimeoutError, asyncio.QueueEmpty):
            return None
    
    def close(self) -> None:
        """关闭队列"""
        self._closed = True
        self._logger.info("Session queue closed")
    
    @property
    def is_closed(self) -> bool:
        """检查队列是否已关闭"""
        return self._closed


class FrameQueue:
    """帧处理队列"""
    
    def __init__(self, max_size: int = 100):
        self._queue = asyncio.Queue(maxsize=max_size)
        self._closed = False
        self._logger = get_logger(f"{__name__}.FrameQueue")
    
    def put_frame(self, frame: np.ndarray) -> None:
        """非阻塞放入帧数据"""
        if not self._closed:
            try:
                self._queue.put_nowait(frame)
                self._logger.debug(f"Put frame into queue, shape: {frame.shape if frame is not None else 'None'}")
            except asyncio.QueueFull:
                # 队列满时丢弃最旧的帧
                self._logger.warning("Frame queue full, dropping oldest frame")
                try:
                    self._queue.get_nowait()
                    self._queue.put_nowait(frame)
                except asyncio.QueueEmpty:
                    pass
    
    async def get_frame(self, timeout: float = None) -> Optional[np.ndarray]:
        """异步获取帧数据"""
        if self._closed:
            return None
        
        try:
            if timeout is None:
                frame = await self._queue.get()
                self._logger.debug("Got frame from queue")
                return frame
            else:
                frame = await asyncio.wait_for(self._queue.get(), timeout=timeout)
                self._logger.debug("Got frame from queue with timeout")
                return frame
        except (asyncio.TimeoutError, asyncio.QueueEmpty):
            return None
    
    def close(self) -> None:
        """关闭队列"""
        self._closed = True
        self._logger.info("Frame queue closed")
    
    @property
    def is_closed(self) -> bool:
        """检查队列是否已关闭"""
        return self._closed


@dataclass
class Session:
    """会话数据模型"""
    session_key: str
    analysis_type: str
    processor: VideoProcessor
    message_queue: SessionQueue = field(default_factory=SessionQueue)
    frame_queue: FrameQueue = field(default_factory=FrameQueue)
    processing_task: Optional[asyncio.Task] = None
    created_at: datetime = field(default_factory=datetime.now)
    last_activity: datetime = field(default_factory=datetime.now)
    status: SessionStatus = SessionStatus.CREATED
    
    def __post_init__(self):
        """初始化后处理"""
        self._logger = get_logger(f"{__name__}.Session")
        self._logger.info(f"Created session {self.session_key} with analysis_type={self.analysis_type}")
    
    def start_processing(self) -> None:
        """启动帧处理协程"""
        if self.processing_task is None or self.processing_task.done():
            self.processing_task = asyncio.create_task(self._process_frames())
            self.status = SessionStatus.PROCESSING
            self._logger.info(f"Started processing for session {self.session_key}")
    
    def stop_processing(self) -> None:
        """停止帧处理协程"""
        if self.processing_task and not self.processing_task.done():
            self.processing_task.cancel()
            self._logger.info(f"Cancelled processing task for session {self.session_key}")
        self.frame_queue.close()
        self.message_queue.close()
        self.status = SessionStatus.STOPPED
        self._logger.info(f"Stopped processing for session {self.session_key}")
    
    async def _process_frames(self) -> None:
        """帧处理协程的主循环"""
        try:
            self.processor.initialize()
            self.status = SessionStatus.ACTIVE
            self._logger.info(f"Processor initialized for session {self.session_key}")
            
            while not self.frame_queue.is_closed:
                frame = await self.frame_queue.get_frame(timeout=1.0)
                if frame is None:
                    continue
                
                self._logger.debug(f"Processing frame for session {self.session_key}, shape: {frame.shape}")
                
                try:
                    # 处理帧数据
                    analysis_data = self.processor.process_frame(frame)
                    
                    # 创建处理结果
                    result = ProcessingResult(
                        session_key=self.session_key,
                        timestamp=datetime.now(),
                        frame_info={
                            "height": frame.shape[0],
                            "width": frame.shape[1],
                            "channels": frame.shape[2] if len(frame.shape) > 2 else 1
                        },
                        analysis_data=analysis_data
                    )
                    
                    # 将结果放入消息队列
                    await self.message_queue.put_result(result)
                    self.last_activity = datetime.now()
                    
                    self._logger.debug(f"Successfully processed and queued result for session {self.session_key}")
                    
                except Exception as e:
                    # 处理错误但继续处理后续帧
                    self._logger.error(f"Error processing frame for session {self.session_key}: {e}", exc_info=True)
                    error_result = ProcessingResult(
                        session_key=self.session_key,
                        timestamp=datetime.now(),
                        frame_info={},
                        analysis_data={},
                        error=str(e)
                    )
                    await self.message_queue.put_result(error_result)
                    
        except asyncio.CancelledError:
            # 协程被取消
            self._logger.info(f"Processing task cancelled for session {self.session_key}")
        except Exception as e:
            self.status = SessionStatus.ERROR
            self._logger.error(f"Error in processing loop for session {self.session_key}: {e}", exc_info=True)
        finally:
            # 清理处理器资源
            try:
                self.processor.cleanup()
                self._logger.info(f"Processor cleaned up for session {self.session_key}")
            except Exception as e:
                self._logger.error(f"Error cleaning up processor for session {self.session_key}: {e}", exc_info=True)
    
    def update_activity(self) -> None:
        """更新最后活动时间"""
        self.last_activity = datetime.now()
    
    def is_expired(self, timeout_seconds: int) -> bool:
        """检查会话是否已过期"""
        expired = (datetime.now() - self.last_activity).total_seconds() > timeout_seconds
        if expired:
            self._logger.info(f"Session {self.session_key} expired after {timeout_seconds} seconds")
        return expired