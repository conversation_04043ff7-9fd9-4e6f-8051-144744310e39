# 单元测试文档

## 概述

本文档描述了RTMP视频处理API系统的单元测试结构和内容。单元测试覆盖了系统的所有核心组件，确保各个模块的功能正确性、错误处理和边界条件处理。

## 测试文件结构

```
tests/
├── README_UNIT_TESTS.md           # 本文档
├── test_api_server.py             # FastAPI服务器测试
├── test_concurrent_access.py      # 并发访问和线程安全测试
├── test_config.py                 # 配置管理系统测试
├── test_error_handling.py         # 错误处理和异常情况测试
├── test_frame_queue.py            # 帧处理队列系统测试
├── test_logging_config.py         # 日志和错误处理系统测试
├── test_message_queue.py          # 消息队列系统测试
├── test_session_manager.py        # 会话管理系统测试
├── test_video_processors.py       # 视频处理器系统测试
├── test_config.toml               # 测试配置文件
└── __init__.py                    # 测试包初始化
```

## 测试覆盖范围

### 1. FastAPI服务器测试 (`test_api_server.py`)

**测试目标**: FastAPI REST API接口的功能正确性

**主要测试内容**:
- **基础端点测试**
  - 根路径端点 (`/`)
  - 健康检查端点 (`/health`)
  - 处理器类型查询端点 (`/api/processors`)

- **会话管理API测试**
  - 创建会话 (`POST /api/session`)
  - 获取会话状态 (`GET /api/session/{session_key}`)
  - 列出所有会话 (`GET /api/sessions`)
  - 删除会话 (`DELETE /api/session/{session_key}`)

- **SSE事件流测试**
  - SSE连接建立 (`GET /events/{session_key}`)
  - 事件数据推送
  - 连接错误处理

- **错误场景测试**
  - 无效处理器类型
  - 不存在的会话
  - 缺少必需参数
  - 完整工作流程测试

**关键测试方法**:
```python
def test_create_session_success()           # 成功创建会话
def test_create_session_invalid_type()      # 无效处理器类型
def test_get_session_status()               # 获取会话状态
def test_sse_events()                       # SSE事件流
def test_session_workflow()                 # 完整工作流程
```

### 2. 并发访问测试 (`test_concurrent_access.py`)

**测试目标**: 系统在高并发场景下的稳定性和线程安全性

**主要测试内容**:
- **SessionManager并发测试**
  - 并发创建会话
  - 并发会话操作（获取、状态查询、移除）
  - 线程安全的会话操作
  - 最大会话数限制下的并发处理

- **队列并发测试**
  - 消息队列的并发生产者/消费者操作
  - 帧队列的并发操作
  - 队列溢出行为测试
  - 内存压力下的处理

- **异常处理测试**
  - 会话管理器错误恢复
  - 处理器错误处理
  - 队列关闭期间的操作
  - 内存压力处理

**关键测试类**:
```python
class TestConcurrentSessionManager        # 并发会话管理测试
class TestConcurrentQueueOperations      # 并发队列操作测试
class TestExceptionHandling              # 异常处理测试
```

### 3. 配置管理测试 (`test_config.py`)

**测试目标**: 配置系统的正确性和验证机制

**主要测试内容**:
- **各配置类测试**
  - RTMPConfig: RTMP服务器配置验证
  - FastAPIConfig: FastAPI服务器配置验证
  - SessionConfig: 会话管理配置验证
  - QueueConfig: 队列配置验证
  - ProcessingConfig: 处理配置验证
  - LoggingConfig: 日志配置验证

- **配置加载测试**
  - 从TOML文件加载配置
  - 从环境变量加载配置
  - 配置验证和错误处理
  - 配置重载机制

**关键测试类**:
```python
class TestRTMPConfig                     # RTMP配置测试
class TestFastAPIConfig                  # FastAPI配置测试
class TestConfig                         # 主配置类测试
class TestConfigModule                   # 配置模块测试
```

### 4. 错误处理测试 (`test_error_handling.py`)

**测试目标**: 系统各层的错误处理机制和异常恢复能力

**主要测试内容**:
- **SessionManager错误处理**
  - 无效分析类型处理
  - 会话超时边界情况
  - 活跃处理会话的清理
  - 管理器停止时的会话处理

- **处理器错误处理**
  - 处理器初始化失败
  - 帧处理失败
  - 处理器清理失败
  - OpenCV不可用时的降级处理

- **队列错误处理**
  - 无效结果处理
  - 无效帧处理
  - 关闭后的队列操作

- **集成错误处理**
  - 完整系统的错误恢复
  - 错误隔离和系统稳定性

**关键测试类**:
```python
class TestSessionManagerErrorHandling    # 会话管理器错误处理
class TestProcessorErrorHandling         # 处理器错误处理
class TestQueueErrorHandling            # 队列错误处理
class TestIntegrationErrorHandling      # 集成错误处理
```

### 5. 帧队列测试 (`test_frame_queue.py`)

**测试目标**: 帧处理队列系统的功能正确性

**主要测试内容**:
- **基础队列操作**
  - 队列创建和初始化
  - 同步放入帧数据
  - 异步获取帧数据
  - 超时获取机制

- **队列行为测试**
  - 队列满时的FIFO行为
  - 队列关闭处理
  - 并发放入和获取操作

- **内存管理测试**
  - 大帧数据的内存管理
  - 不同格式帧数据的处理

**关键测试方法**:
```python
def test_put_frame_sync()               # 同步放入帧
def test_get_frame_async()              # 异步获取帧
def test_queue_full_fifo_behavior()     # FIFO行为
def test_concurrent_put_get()           # 并发操作
```

### 6. 日志配置测试 (`test_logging_config.py`)

**测试目标**: 日志系统和错误处理系统的功能正确性

**主要测试内容**:
- **结构化日志格式化器测试**
  - 基本格式化
  - 会话密钥和操作类型格式化
  - 异常信息格式化
  - 额外数据格式化

- **会话日志适配器测试**
  - 基本日志记录
  - 操作类型设置
  - 上下文信息传递

- **错误处理器测试**
  - API错误处理器
  - RTMP错误处理器
  - SSE错误处理器
  - 视频处理错误处理器

- **日志管理器测试**
  - 日志系统初始化
  - 日志记录器获取
  - 错误处理器获取

**关键测试类**:
```python
class TestStructuredFormatter           # 结构化格式化器测试
class TestSessionLoggerAdapter          # 会话日志适配器测试
class TestAPIErrorHandler              # API错误处理器测试
class TestLoggingManager               # 日志管理器测试
```

### 7. 消息队列测试 (`test_message_queue.py`)

**测试目标**: 消息队列系统的功能正确性

**主要测试内容**:
- **基础队列操作**
  - 队列创建和初始化
  - 放入和获取处理结果
  - 超时获取机制

- **队列行为测试**
  - 队列满时的行为
  - 队列关闭处理
  - 并发操作

- **内存管理测试**
  - 大数据的内存管理
  - 队列大小限制

**关键测试方法**:
```python
def test_put_and_get_result()           # 基本放入获取操作
def test_queue_full_behavior()          # 队列满行为
def test_concurrent_operations()        # 并发操作
def test_memory_management()            # 内存管理
```

### 8. 会话管理测试 (`test_session_manager.py`)

**测试目标**: 会话管理系统的核心功能

**主要测试内容**:
- **会话基础操作**
  - 创建会话
  - 获取会话
  - 会话状态查询
  - 列出所有会话
  - 移除会话

- **会话限制和清理**
  - 最大并发会话数限制
  - 会话超时清理
  - 帧处理功能

- **处理器工厂测试**
  - 获取可用处理器类型
  - 创建处理器实例
  - 未知处理器类型处理

**关键测试类**:
```python
class TestSessionManager               # 会话管理器测试
class TestProcessorFactory            # 处理器工厂测试
class TestSessionManagerIntegration   # 会话管理器集成测试
```

### 9. 视频处理器测试 (`test_video_processors.py`)

**测试目标**: 视频处理器系统的功能正确性

**主要测试内容**:
- **DummyProcessor测试**
  - 处理器创建和初始化
  - 单帧和多帧处理
  - 不同格式帧处理
  - 清理功能

- **OpenCVPreviewProcessor测试**
  - 有/无OpenCV环境的初始化
  - 基础和高级帧处理功能
  - OpenCV错误处理
  - 灰度帧处理

- **ProcessorFactory测试**
  - 可用处理器类型获取
  - 处理器实例创建
  - 新处理器类型注册
  - 接口规范验证

**关键测试类**:
```python
class TestDummyProcessor               # DummyProcessor测试
class TestOpenCVPreviewProcessor       # OpenCVPreviewProcessor测试
class TestProcessorFactory            # ProcessorFactory测试
class TestProcessorIntegration        # 处理器集成测试
```

## 测试运行方式

### 运行所有单元测试
```bash
# 使用pytest运行所有测试
pytest tests/ -v

# 排除集成测试，只运行单元测试
pytest tests/ -v -k "not integration"

# 运行特定测试文件
pytest tests/test_session_manager.py -v
```

### 运行特定测试类或方法
```bash
# 运行特定测试类
pytest tests/test_session_manager.py::TestSessionManager -v

# 运行特定测试方法
pytest tests/test_session_manager.py::TestSessionManager::test_create_session -v
```

### 生成测试覆盖率报告
```bash
# 安装coverage工具
pip install coverage

# 运行测试并生成覆盖率报告
coverage run -m pytest tests/ -k "not integration"
coverage report
coverage html  # 生成HTML报告
```

## 测试配置

### pytest配置 (`pytest.ini`)
```ini
[tool:pytest]
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*
addopts = -v --tb=short
asyncio_mode = auto
markers =
    integration: marks tests as integration tests
    slow: marks tests as slow running
```

### 测试配置文件 (`test_config.toml`)
测试使用独立的配置文件，避免影响生产环境配置。

## 测试最佳实践

### 1. 测试隔离
- 每个测试方法都是独立的，不依赖其他测试的状态
- 使用pytest fixtures管理测试资源的创建和清理
- 异步测试使用`@pytest.mark.asyncio`装饰器

### 2. 模拟和存根
- 使用`unittest.mock`模拟外部依赖
- 对于OpenCV等可选依赖，使用条件模拟
- 模拟网络连接和文件系统操作

### 3. 异常测试
- 使用`pytest.raises`测试异常情况
- 验证异常消息的准确性
- 测试异常后的系统状态

### 4. 并发测试
- 使用`asyncio.gather`测试异步并发
- 使用`ThreadPoolExecutor`测试线程并发
- 验证线程安全和数据一致性

### 5. 资源管理
- 使用context managers确保资源正确清理
- 异步资源使用async context managers
- 测试结束后验证资源释放

## 测试数据管理

### 测试帧数据
```python
# 创建标准测试帧
test_frame = np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)

# 创建不同格式的测试帧
grayscale_frame = np.random.randint(0, 255, (240, 320), dtype=np.uint8)
hd_frame = np.random.randint(0, 255, (720, 1280, 3), dtype=np.uint8)
```

### 测试结果数据
```python
# 创建标准测试结果
test_result = ProcessingResult(
    session_key="test-session",
    timestamp=datetime.now(),
    frame_info={"width": 640, "height": 480},
    analysis_data={"test": "data"}
)
```

## 持续集成

### GitHub Actions配置示例
```yaml
name: Unit Tests
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v2
    - name: Set up Python
      uses: actions/setup-python@v2
      with:
        python-version: 3.9
    - name: Install dependencies
      run: |
        pip install -r requirements.txt
        pip install pytest coverage
    - name: Run unit tests
      run: |
        coverage run -m pytest tests/ -k "not integration"
        coverage report
```

## 性能测试注意事项

虽然这些是单元测试，但某些测试涉及性能相关的验证：

### 1. 队列性能
- 测试大量数据的队列操作性能
- 验证内存使用不会无限增长
- 测试队列满时的处理效率

### 2. 并发性能
- 测试高并发场景下的响应时间
- 验证线程安全不会显著影响性能
- 测试资源竞争情况

### 3. 内存管理
- 测试大帧数据的内存使用
- 验证对象正确释放
- 测试内存泄漏情况

## 故障排除

### 常见测试失败原因

1. **异步测试超时**
   - 增加超时时间
   - 检查异步操作是否正确等待
   - 验证事件循环配置

2. **并发测试不稳定**
   - 增加同步点
   - 使用更可靠的等待机制
   - 减少时间依赖的断言

3. **资源清理问题**
   - 确保所有异步任务正确取消
   - 验证文件句柄和网络连接关闭
   - 使用适当的cleanup fixtures

4. **模拟对象问题**
   - 验证模拟对象的配置正确
   - 检查模拟对象的调用次数和参数
   - 确保模拟对象在正确的作用域内

## 测试维护

### 定期维护任务
1. **更新测试数据**: 确保测试数据反映实际使用场景
2. **重构测试代码**: 消除重复代码，提高测试可读性
3. **更新模拟对象**: 保持模拟对象与实际接口同步
4. **性能基准更新**: 根据系统改进更新性能期望

### 新功能测试
添加新功能时，确保：
1. 编写对应的单元测试
2. 更新相关的集成测试
3. 考虑边界条件和错误情况
4. 验证与现有功能的兼容性

这个单元测试套件为RTMP视频处理API系统提供了全面的测试覆盖，确保系统的可靠性、稳定性和正确性。通过持续运行这些测试，可以及早发现问题并保证代码质量。