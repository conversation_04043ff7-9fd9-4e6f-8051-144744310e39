# 集成测试专用配置文件
# 使用不同的端口避免与主应用冲突

[rtmp]
host = "127.0.0.1"
port = 1936  # 不同于主应用的1935端口

[fastapi]
host = "127.0.0.1"
port = 8001  # 不同于主应用的8000端口
log_level = "info"
reload = false
cors_origins = ["*"]

[session]
timeout = 300  # 5分钟超时，适合测试
max_concurrent_sessions = 10  # 较小的并发数用于测试
cleanup_interval = 60  # 60秒清理间隔

[queue]
max_size = 100  # 较小的队列大小
timeout = 2.0  # 较短的超时时间
frame_queue_size = 50  # 较小的帧队列大小

[processing]
max_frame_rate = 10  # 较低的处理帧率用于测试
frame_skip_threshold = 3  # 较小的跳帧阈值
processing_timeout = 3.0  # 较短的处理超时时间

[logging]
level = "INFO"
format = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
file_path = "logs/test_app.log"
max_file_size = 10485760  # 日志文件最大大小（10MB）
backup_count = 3  # 日志文件备份数量