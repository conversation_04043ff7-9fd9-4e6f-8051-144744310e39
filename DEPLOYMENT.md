# 部署指南

本文档提供RTMP视频处理API的详细部署指南，包括开发环境、生产环境和Docker部署方式。

## 目录

- [系统要求](#系统要求)
- [开发环境部署](#开发环境部署)
- [生产环境部署](#生产环境部署)
- [Docker部署](#docker部署)
- [配置说明](#配置说明)
- [性能调优](#性能调优)
- [监控和维护](#监控和维护)
- [故障排除](#故障排除)

## 系统要求

### 最低要求
- **操作系统**: Linux (Ubuntu 18.04+), Windows 10+, macOS 10.15+
- **Python**: 3.8+
- **内存**: 2GB RAM
- **存储**: 1GB 可用空间
- **网络**: 支持TCP端口1935 (RTMP) 和8000 (HTTP)

### 推荐配置
- **CPU**: 4核心以上
- **内存**: 8GB RAM
- **存储**: SSD 10GB+
- **网络**: 千兆网络

### 外部依赖
- **FFmpeg**: 用于RTMP流处理和测试
- **OpenCV**: 用于视频处理器（可选）

## 开发环境部署

### 1. 环境准备

#### 安装Python
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install python3.8 python3.8-venv python3.8-dev

# CentOS/RHEL
sudo yum install python38 python38-venv python38-devel

# Windows
# 从 https://python.org 下载并安装Python 3.8+

# macOS
brew install python@3.8
```

#### 安装FFmpeg
```bash
# Ubuntu/Debian
sudo apt install ffmpeg

# CentOS/RHEL
sudo yum install epel-release
sudo yum install ffmpeg

# Windows
# 从 https://ffmpeg.org 下载并添加到PATH

# macOS
brew install ffmpeg
```

### 2. 项目设置

```bash
# 克隆项目
git clone <repository-url>
cd rtmp-video-processing-api

# 创建虚拟环境
python3 -m venv .venv

# 激活虚拟环境
source .venv/bin/activate  # Linux/macOS
# 或
.venv\Scripts\activate     # Windows

# 升级pip
pip install --upgrade pip

# 安装依赖
pip install -r requirements.txt

# 安装开发依赖（可选）
pip install pytest pytest-asyncio pytest-cov black flake8
```

### 3. 配置文件

创建开发配置文件 `config.toml`:

```toml
[rtmp]
host = "0.0.0.0"
port = 1935

[fastapi]
host = "0.0.0.0"
port = 8000
reload = true
log_level = "debug"

[session]
timeout = 1800  # 30分钟
max_concurrent_sessions = 10
cleanup_interval = 300

[logging]
level = "DEBUG"
file_path = "logs/dev.log"
```

### 4. 启动开发服务器

```bash
# 创建日志目录
mkdir -p logs

# 启动服务
python main.py
```

### 5. 验证安装

```bash
# 检查API服务
curl http://localhost:8000/health

# 检查API文档
# 浏览器访问: http://localhost:8000/docs

# 运行测试
python -m pytest tests/ -v

# 运行示例客户端
python example_client.py
```

## 生产环境部署

### 1. 系统准备

#### 创建专用用户
```bash
# 创建应用用户
sudo useradd -r -s /bin/false rtmp-api
sudo mkdir -p /opt/rtmp-api
sudo chown rtmp-api:rtmp-api /opt/rtmp-api
```

#### 安装系统依赖
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install python3.8 python3.8-venv python3.8-dev \
                 ffmpeg nginx supervisor logrotate

# CentOS/RHEL
sudo yum install python38 python38-venv python38-devel \
                 ffmpeg nginx supervisor logrotate
```

### 2. 应用部署

```bash
# 切换到应用目录
cd /opt/rtmp-api

# 部署应用代码
sudo -u rtmp-api git clone <repository-url> .

# 创建虚拟环境
sudo -u rtmp-api python3 -m venv venv

# 安装依赖
sudo -u rtmp-api ./venv/bin/pip install -r requirements.txt

# 创建必要目录
sudo -u rtmp-api mkdir -p logs temp
```

### 3. 生产配置

创建生产配置文件 `/opt/rtmp-api/config.toml`:

```toml
[rtmp]
host = "0.0.0.0"
port = 1935

[fastapi]
host = "127.0.0.1"  # 仅本地访问，通过Nginx代理
port = 8000
reload = false
log_level = "info"

[session]
timeout = 3600
max_concurrent_sessions = 100
cleanup_interval = 300

[queue]
max_size = 1000
timeout = 1.0
frame_queue_size = 100

[processing]
max_frame_rate = 30
frame_skip_threshold = 5
processing_timeout = 5.0

[logging]
level = "INFO"
file_path = "/opt/rtmp-api/logs/app.log"
max_file_size = 52428800  # 50MB
backup_count = 10
```

### 4. Supervisor配置

创建 `/etc/supervisor/conf.d/rtmp-api.conf`:

```ini
[program:rtmp-api]
command=/opt/rtmp-api/venv/bin/python main.py
directory=/opt/rtmp-api
user=rtmp-api
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/opt/rtmp-api/logs/supervisor.log
stdout_logfile_maxbytes=50MB
stdout_logfile_backups=10
environment=PYTHONPATH="/opt/rtmp-api"
```

### 5. Nginx配置

创建 `/etc/nginx/sites-available/rtmp-api`:

```nginx
upstream rtmp_api {
    server 127.0.0.1:8000;
}

server {
    listen 80;
    server_name your-domain.com;

    # 安全头
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";

    # API代理
    location / {
        proxy_pass http://rtmp_api;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # WebSocket和SSE支持
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_cache_bypass $http_upgrade;
        
        # 超时设置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }

    # SSE特殊配置
    location /events/ {
        proxy_pass http://rtmp_api;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        
        # SSE必需配置
        proxy_buffering off;
        proxy_cache off;
        proxy_set_header Connection '';
        proxy_http_version 1.1;
        chunked_transfer_encoding off;
        
        # 长连接支持
        proxy_read_timeout 24h;
        proxy_send_timeout 24h;
    }

    # 静态文件（如果有）
    location /static/ {
        alias /opt/rtmp-api/static/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # 健康检查
    location /health {
        proxy_pass http://rtmp_api;
        access_log off;
    }
}
```

### 6. 防火墙配置

```bash
# Ubuntu (ufw)
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw allow 1935/tcp
sudo ufw enable

# CentOS/RHEL (firewalld)
sudo firewall-cmd --permanent --add-port=80/tcp
sudo firewall-cmd --permanent --add-port=443/tcp
sudo firewall-cmd --permanent --add-port=1935/tcp
sudo firewall-cmd --reload
```

### 7. 启动服务

```bash
# 启用Nginx站点
sudo ln -s /etc/nginx/sites-available/rtmp-api /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl restart nginx

# 启动Supervisor
sudo supervisorctl reread
sudo supervisorctl update
sudo supervisorctl start rtmp-api

# 检查状态
sudo supervisorctl status rtmp-api
```

### 8. SSL配置（推荐）

使用Let's Encrypt配置HTTPS:

```bash
# 安装Certbot
sudo apt install certbot python3-certbot-nginx

# 获取SSL证书
sudo certbot --nginx -d your-domain.com

# 自动续期
sudo crontab -e
# 添加: 0 12 * * * /usr/bin/certbot renew --quiet
```

## Docker部署

### 1. Dockerfile

创建 `Dockerfile`:

```dockerfile
FROM python:3.8-slim

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    ffmpeg \
    libgl1-mesa-glx \
    libglib2.0-0 \
    && rm -rf /var/lib/apt/lists/*

# 创建应用用户
RUN useradd -r -s /bin/false rtmpapi

# 设置工作目录
WORKDIR /app

# 复制依赖文件
COPY requirements.txt .

# 安装Python依赖
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY . .

# 创建日志目录
RUN mkdir -p logs temp && \
    chown -R rtmpapi:rtmpapi /app

# 切换到应用用户
USER rtmpapi

# 暴露端口
EXPOSE 8000 1935

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# 启动命令
CMD ["python", "main.py"]
```

### 2. Docker Compose

创建 `docker-compose.yml`:

```yaml
version: '3.8'

services:
  rtmp-api:
    build: .
    ports:
      - "8000:8000"
      - "1935:1935"
    volumes:
      - ./logs:/app/logs
      - ./config.toml:/app/config.toml:ro
    environment:
      - APP_LOGGING_LEVEL=INFO
      - APP_LOGGING_FILE_PATH=/app/logs/app.log
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - rtmp-api
    restart: unless-stopped
```

### 3. 构建和运行

```bash
# 构建镜像
docker-compose build

# 启动服务
docker-compose up -d

# 查看日志
docker-compose logs -f rtmp-api

# 停止服务
docker-compose down
```

## 配置说明

### 环境变量优先级

配置加载优先级（从高到低）：
1. 环境变量
2. config.toml文件
3. 默认值

### 关键配置项

#### RTMP配置
- `APP_RTMP_HOST`: RTMP服务器监听地址
- `APP_RTMP_PORT`: RTMP服务器端口

#### API配置
- `APP_FASTAPI_HOST`: API服务器监听地址
- `APP_FASTAPI_PORT`: API服务器端口
- `APP_FASTAPI_LOG_LEVEL`: API日志级别

#### 会话配置
- `APP_SESSION_TIMEOUT`: 会话超时时间（秒）
- `APP_SESSION_MAX_CONCURRENT`: 最大并发会话数

#### 日志配置
- `APP_LOGGING_LEVEL`: 日志级别
- `APP_LOGGING_FILE_PATH`: 日志文件路径

## 性能调优

### 系统级优化

#### 内核参数调优
```bash
# 编辑 /etc/sysctl.conf
net.core.somaxconn = 65535
net.core.netdev_max_backlog = 5000
net.ipv4.tcp_max_syn_backlog = 65535
net.ipv4.tcp_fin_timeout = 30
net.ipv4.tcp_keepalive_time = 1200
net.ipv4.tcp_max_tw_buckets = 5000

# 应用配置
sudo sysctl -p
```

#### 文件描述符限制
```bash
# 编辑 /etc/security/limits.conf
rtmp-api soft nofile 65535
rtmp-api hard nofile 65535

# 或者在systemd服务中设置
LimitNOFILE=65535
```

### 应用级优化

#### 队列大小调优
```toml
[queue]
max_size = 2000          # 增加队列大小
frame_queue_size = 200   # 增加帧队列大小
timeout = 0.5            # 减少超时时间
```

#### 处理器优化
```toml
[processing]
max_frame_rate = 60      # 提高最大帧率
frame_skip_threshold = 3 # 降低跳帧阈值
processing_timeout = 3.0 # 减少处理超时
```

### 监控指标

关键性能指标：
- CPU使用率
- 内存使用率
- 网络I/O
- 活跃会话数
- 队列长度
- 处理延迟

## 监控和维护

### 日志监控

#### 日志轮转配置
创建 `/etc/logrotate.d/rtmp-api`:

```
/opt/rtmp-api/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 rtmp-api rtmp-api
    postrotate
        supervisorctl restart rtmp-api
    endscript
}
```

#### 日志分析
```bash
# 查看错误日志
grep ERROR /opt/rtmp-api/logs/app.log

# 统计会话创建
grep "Session created" /opt/rtmp-api/logs/app.log | wc -l

# 监控实时日志
tail -f /opt/rtmp-api/logs/app.log
```

### 健康检查

#### 系统健康检查脚本
创建 `/opt/rtmp-api/health_check.sh`:

```bash
#!/bin/bash

# API健康检查
if ! curl -f http://localhost:8000/health > /dev/null 2>&1; then
    echo "API health check failed"
    exit 1
fi

# 检查进程
if ! pgrep -f "python main.py" > /dev/null; then
    echo "Main process not running"
    exit 1
fi

# 检查端口
if ! netstat -ln | grep :1935 > /dev/null; then
    echo "RTMP port not listening"
    exit 1
fi

echo "All checks passed"
exit 0
```

#### 定期维护任务
```bash
# 添加到crontab
0 2 * * * /opt/rtmp-api/health_check.sh
0 3 * * 0 /opt/rtmp-api/cleanup_old_logs.sh
```

### 备份策略

#### 配置备份
```bash
# 备份配置文件
tar -czf /backup/rtmp-api-config-$(date +%Y%m%d).tar.gz \
    /opt/rtmp-api/config.toml \
    /etc/supervisor/conf.d/rtmp-api.conf \
    /etc/nginx/sites-available/rtmp-api
```

#### 日志备份
```bash
# 压缩并备份旧日志
find /opt/rtmp-api/logs -name "*.log" -mtime +7 -exec gzip {} \;
find /opt/rtmp-api/logs -name "*.gz" -mtime +30 -delete
```

## 故障排除

### 常见问题

#### 1. RTMP连接失败
**症状**: 客户端无法连接到RTMP服务器

**排查步骤**:
```bash
# 检查端口监听
netstat -ln | grep 1935

# 检查防火墙
sudo ufw status
sudo iptables -L

# 检查进程
ps aux | grep python

# 查看日志
tail -f /opt/rtmp-api/logs/app.log
```

#### 2. API响应慢
**症状**: API请求响应时间过长

**排查步骤**:
```bash
# 检查系统负载
top
htop

# 检查内存使用
free -h

# 检查磁盘I/O
iostat -x 1

# 分析慢查询日志
grep "slow" /opt/rtmp-api/logs/app.log
```

#### 3. 会话创建失败
**症状**: 无法创建新会话

**排查步骤**:
```bash
# 检查会话数量
curl http://localhost:8000/api/sessions | jq length

# 检查配置
grep max_concurrent_sessions /opt/rtmp-api/config.toml

# 查看错误日志
grep "create_session" /opt/rtmp-api/logs/app.log | tail -20
```

#### 4. SSE连接中断
**症状**: SSE连接频繁断开

**排查步骤**:
```bash
# 检查Nginx配置
nginx -t

# 检查代理设置
curl -v http://localhost:8000/events/test-session

# 查看连接日志
grep "SSE" /opt/rtmp-api/logs/app.log
```

### 紧急恢复

#### 服务重启
```bash
# 重启应用
sudo supervisorctl restart rtmp-api

# 重启Nginx
sudo systemctl restart nginx

# 完全重启
sudo supervisorctl stop rtmp-api
sudo systemctl stop nginx
sudo systemctl start nginx
sudo supervisorctl start rtmp-api
```

#### 配置回滚
```bash
# 恢复配置备份
sudo cp /backup/config.toml.backup /opt/rtmp-api/config.toml
sudo supervisorctl restart rtmp-api
```

### 性能分析

#### 使用profiling工具
```bash
# 安装profiling工具
pip install py-spy

# 分析运行中的进程
sudo py-spy top --pid $(pgrep -f "python main.py")

# 生成火焰图
sudo py-spy record -o profile.svg --pid $(pgrep -f "python main.py")
```

#### 内存分析
```bash
# 安装内存分析工具
pip install memory-profiler

# 分析内存使用
python -m memory_profiler main.py
```

## 安全建议

### 网络安全
- 使用防火墙限制访问
- 配置SSL/TLS加密
- 实施访问控制
- 定期更新依赖

### 应用安全
- 输入验证和清理
- 会话超时管理
- 错误信息脱敏
- 日志审计

### 系统安全
- 最小权限原则
- 定期安全更新
- 监控异常活动
- 备份和恢复计划

---

如有问题，请查看项目文档或提交Issue。