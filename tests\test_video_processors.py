"""
测试视频处理器系统
"""
import pytest
import numpy as np
from unittest.mock import Mock, patch, MagicMock

from processors.processors import OpenCVPreviewProcessor, DummyProcessor
from models import VideoProcessor
from server.session_manager import ProcessorFactory


class TestDummyProcessor:
    """测试DummyProcessor类"""
    
    def test_processor_creation(self):
        """测试处理器创建"""
        processor = DummyProcessor()
        assert processor is not None
        assert not processor._initialized
        assert processor._frame_count == 0
    
    def test_processor_initialization(self):
        """测试处理器初始化"""
        processor = DummyProcessor()
        
        # 初始化前应该未初始化
        assert not processor._initialized
        
        # 初始化
        processor.initialize()
        
        # 初始化后应该已初始化
        assert processor._initialized
        assert processor._frame_count == 0
    
    def test_process_frame_without_initialization(self):
        """测试未初始化时处理帧"""
        processor = DummyProcessor()
        frame = np.zeros((100, 100, 3), dtype=np.uint8)
        
        # 未初始化时应该抛出异常
        with pytest.raises(RuntimeError, match="Processor not initialized"):
            processor.process_frame(frame)
    
    def test_process_single_frame(self):
        """测试处理单帧"""
        processor = DummyProcessor()
        processor.initialize()
        
        # 创建测试帧
        frame = np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)
        
        # 处理帧
        result = processor.process_frame(frame)
        
        # 验证结果
        assert isinstance(result, dict)
        assert result["processor_type"] == "dummy"
        assert result["frame_number"] == 1
        assert result["dimensions"]["width"] == 640
        assert result["dimensions"]["height"] == 480
        assert result["dimensions"]["channels"] == 3
        assert "timestamp" in result
        assert "message" in result
    
    def test_process_multiple_frames(self):
        """测试处理多帧"""
        processor = DummyProcessor()
        processor.initialize()
        
        results = []
        for i in range(5):
            frame = np.random.randint(0, 255, (100, 200, 3), dtype=np.uint8)
            result = processor.process_frame(frame)
            results.append(result)
        
        # 验证帧计数递增
        for i, result in enumerate(results):
            assert result["frame_number"] == i + 1
            assert result["timestamp"] == (i + 1) * 0.033
    
    def test_process_grayscale_frame(self):
        """测试处理灰度帧"""
        processor = DummyProcessor()
        processor.initialize()
        
        # 创建灰度帧
        frame = np.random.randint(0, 255, (240, 320), dtype=np.uint8)
        
        # 处理帧
        result = processor.process_frame(frame)
        
        # 验证结果
        assert result["dimensions"]["width"] == 320
        assert result["dimensions"]["height"] == 240
        assert result["dimensions"]["channels"] == 1
    
    def test_cleanup(self):
        """测试清理"""
        processor = DummyProcessor()
        processor.initialize()
        
        # 处理一些帧
        frame = np.zeros((100, 100, 3), dtype=np.uint8)
        processor.process_frame(frame)
        processor.process_frame(frame)
        
        # 清理
        processor.cleanup()
        
        # 验证状态重置
        assert not processor._initialized
        assert processor._frame_count == 0


class TestOpenCVPreviewProcessor:
    """测试OpenCVPreviewProcessor类"""
    
    def test_processor_creation(self):
        """测试处理器创建"""
        processor = OpenCVPreviewProcessor()
        assert processor is not None
        assert not processor._initialized
        assert processor._frame_count == 0
    
    def test_initialization_with_opencv(self):
        """测试有OpenCV时的初始化"""
        processor = OpenCVPreviewProcessor()
        
        # 模拟OpenCV可用
        mock_cv2 = MagicMock()
        with patch.dict('sys.modules', {'cv2': mock_cv2}):
            processor.initialize()
            
            assert processor._initialized
            assert processor._cv2 == mock_cv2
            assert processor._frame_count == 0
    
    def test_initialization_without_opencv(self):
        """测试没有OpenCV时的初始化"""
        processor = OpenCVPreviewProcessor()
        
        # 直接模拟sys.modules中没有cv2模块
        import sys
        with patch.dict(sys.modules, {'cv2': None}):
            processor.initialize()
        
        assert processor._initialized
        assert processor._cv2 is None
        assert processor._frame_count == 0
    
    def test_process_frame_without_opencv(self):
        """测试没有OpenCV时处理帧"""
        processor = OpenCVPreviewProcessor()
        
        # 直接模拟sys.modules中没有cv2模块
        import sys
        with patch.dict(sys.modules, {'cv2': None}):
            processor.initialize()
        
        # 创建测试帧
        frame = np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)
        
        # 处理帧
        result = processor.process_frame(frame)
        
        # 验证基本结果
        assert isinstance(result, dict)
        assert result["frame_number"] == 1
        assert result["dimensions"]["width"] == 640
        assert result["dimensions"]["height"] == 480
        assert result["dimensions"]["channels"] == 3
        assert result["data_type"] == "uint8"
        assert result["size_bytes"] == frame.nbytes
        
        # 不应该有高级分析结果
        assert "statistics" not in result
        assert "histogram" not in result
    
    def test_process_frame_with_opencv(self):
        """测试有OpenCV时处理帧"""
        processor = OpenCVPreviewProcessor()
        
        # 模拟OpenCV
        mock_cv2 = MagicMock()
        mock_cv2.cvtColor.return_value = np.random.randint(0, 255, (480, 640), dtype=np.uint8)
        mock_cv2.calcHist.return_value = np.random.randint(0, 1000, (256, 1), dtype=np.uint32)
        mock_cv2.COLOR_BGR2GRAY = 6  # OpenCV常量
        
        with patch.dict('sys.modules', {'cv2': mock_cv2}):
            processor.initialize()
            
            # 创建测试帧
            frame = np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)
            
            # 处理帧
            result = processor.process_frame(frame)
            
            # 验证基本结果
            assert result["frame_number"] == 1
            assert result["dimensions"]["width"] == 640
            assert result["dimensions"]["height"] == 480
            assert result["dimensions"]["channels"] == 3
            
            # 验证高级分析结果
            assert "statistics" in result
            assert "mean" in result["statistics"]
            assert "std" in result["statistics"]
            assert "min" in result["statistics"]
            assert "max" in result["statistics"]
            
            assert "histogram" in result
            assert "bins" in result["histogram"]
            assert "peak_value" in result["histogram"]
            assert "peak_count" in result["histogram"]
    
    def test_process_grayscale_frame_with_opencv(self):
        """测试有OpenCV时处理灰度帧"""
        processor = OpenCVPreviewProcessor()
        
        # 模拟OpenCV
        mock_cv2 = MagicMock()
        
        with patch.dict('sys.modules', {'cv2': mock_cv2}):
            processor.initialize()
            
            # 创建灰度帧
            frame = np.random.randint(0, 255, (240, 320), dtype=np.uint8)
            
            # 处理帧
            result = processor.process_frame(frame)
            
            # 验证结果
            assert result["dimensions"]["channels"] == 1
            assert "statistics" in result
            # 灰度图像不应该有直方图分析（因为不是3通道）
            assert "histogram" not in result
    
    def test_opencv_error_handling(self):
        """测试OpenCV操作错误处理"""
        processor = OpenCVPreviewProcessor()
        
        # 模拟OpenCV操作出错
        mock_cv2 = MagicMock()
        mock_cv2.cvtColor.side_effect = Exception("OpenCV error")
        
        with patch.dict('sys.modules', {'cv2': mock_cv2}):
            processor.initialize()
            
            # 创建测试帧
            frame = np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)
            
            # 处理帧应该不会崩溃
            result = processor.process_frame(frame)
            
            # 基本信息应该存在
            assert result["frame_number"] == 1
            assert result["dimensions"]["width"] == 640
            
            # 高级分析可能不存在或不完整
            # 但不应该抛出异常
    
    def test_process_frame_without_initialization(self):
        """测试未初始化时处理帧"""
        processor = OpenCVPreviewProcessor()
        frame = np.zeros((100, 100, 3), dtype=np.uint8)
        
        # 未初始化时应该抛出异常
        with pytest.raises(RuntimeError, match="Processor not initialized"):
            processor.process_frame(frame)
    
    def test_cleanup(self):
        """测试清理"""
        processor = OpenCVPreviewProcessor()
        
        mock_cv2 = MagicMock()
        with patch.dict('sys.modules', {'cv2': mock_cv2}):
            processor.initialize()
            
            # 处理一些帧
            frame = np.zeros((100, 100, 3), dtype=np.uint8)
            processor.process_frame(frame)
            processor.process_frame(frame)
            
            # 清理
            processor.cleanup()
            
            # 验证状态重置
            assert not processor._initialized
            assert processor._frame_count == 0


class TestProcessorFactory:
    """测试ProcessorFactory类"""
    
    def test_get_available_types(self):
        """测试获取可用处理器类型"""
        available_types = ProcessorFactory.get_available_types()
        
        # 验证默认处理器类型存在
        assert isinstance(available_types, list)
        assert "dummy" in available_types
        assert "opencv_preview" in available_types
        assert len(available_types) >= 2
    
    def test_create_dummy_processor(self):
        """测试创建DummyProcessor"""
        processor = ProcessorFactory.create_processor("dummy")
        
        assert isinstance(processor, DummyProcessor)
        assert isinstance(processor, VideoProcessor)
    
    def test_create_opencv_processor(self):
        """测试创建OpenCVPreviewProcessor"""
        processor = ProcessorFactory.create_processor("opencv_preview")
        
        assert isinstance(processor, OpenCVPreviewProcessor)
        assert isinstance(processor, VideoProcessor)
    
    def test_create_unknown_processor(self):
        """测试创建未知类型的处理器"""
        with pytest.raises(ValueError, match="Unknown processor type"):
            ProcessorFactory.create_processor("unknown_type")
    
    def test_register_new_processor(self):
        """测试注册新的处理器类型"""
        # 创建测试处理器类
        class TestProcessor(VideoProcessor):
            def initialize(self):
                pass
            
            def process_frame(self, frame):
                return {"test": True}
            
            def cleanup(self):
                pass
        
        # 保存原始的处理器列表
        original_processors = ProcessorFactory._processors.copy()
        
        try:
            # 注册处理器
            ProcessorFactory.register_processor("test_processor", TestProcessor)
            
            # 验证注册成功
            available_types = ProcessorFactory.get_available_types()
            assert "test_processor" in available_types
            
            # 验证可以创建
            processor = ProcessorFactory.create_processor("test_processor")
            assert isinstance(processor, TestProcessor)
        
        finally:
            # 恢复原始的处理器列表，避免影响其他测试
            ProcessorFactory._processors = original_processors
    
    def test_processor_interface_compliance(self):
        """测试所有处理器都符合接口规范"""
        available_types = ProcessorFactory.get_available_types()
        
        for proc_type in available_types:
            processor = ProcessorFactory.create_processor(proc_type)
            
            # 验证是VideoProcessor的实例
            assert isinstance(processor, VideoProcessor)
            
            # 验证有必需的方法
            assert hasattr(processor, 'initialize')
            assert hasattr(processor, 'process_frame')
            assert hasattr(processor, 'cleanup')
            
            # 验证方法可调用
            assert callable(processor.initialize)
            assert callable(processor.process_frame)
            assert callable(processor.cleanup)


@pytest.mark.integration
class TestProcessorIntegration:
    """处理器集成测试"""
    
    def test_full_processor_lifecycle(self):
        """测试处理器完整生命周期"""
        # 只测试已知的标准处理器类型，避免测试过程中注册的临时处理器
        standard_processor_types = ["dummy", "opencv_preview"]
        
        for proc_type in standard_processor_types:
            processor = ProcessorFactory.create_processor(proc_type)
            
            # 1. 初始化
            processor.initialize()
            
            # 2. 处理多种格式的帧
            test_frames = [
                np.random.randint(0, 255, (100, 100, 3), dtype=np.uint8),  # RGB
                np.random.randint(0, 255, (200, 300), dtype=np.uint8),     # 灰度
                np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8),  # 标准分辨率
            ]
            
            results = []
            for frame in test_frames:
                result = processor.process_frame(frame)
                results.append(result)
                
                # 验证结果格式
                assert isinstance(result, dict)
                # 对于标准处理器，验证包含预期的字段
                if proc_type == "dummy":
                    assert "frame_number" in result
                    assert "dimensions" in result
                    assert "processor_type" in result
                elif proc_type == "opencv_preview":
                    assert "frame_number" in result
                    assert "dimensions" in result
            
            # 3. 清理
            processor.cleanup()
            
            # 验证处理了所有帧
            assert len(results) == len(test_frames)
    
    def test_processor_error_recovery(self):
        """测试处理器错误恢复"""
        processor = DummyProcessor()
        processor.initialize()
        
        # 正常处理
        normal_frame = np.random.randint(0, 255, (100, 100, 3), dtype=np.uint8)
        result1 = processor.process_frame(normal_frame)
        assert result1["frame_number"] == 1
        
        # 模拟异常帧（但不应该导致处理器崩溃）
        try:
            # 创建一个可能导致问题的帧
            problematic_frame = np.array([])  # 空数组
            # 这可能会在某些处理器中引发异常，但我们测试的是错误处理
            processor.process_frame(problematic_frame)
        except Exception:
            # 如果抛出异常，这是预期的
            pass
        
        # 继续正常处理
        result2 = processor.process_frame(normal_frame)
        # 帧计数应该继续（取决于实现）
        assert isinstance(result2, dict)
        
        processor.cleanup()


if __name__ == "__main__":
    pytest.main([__file__, "-v"])