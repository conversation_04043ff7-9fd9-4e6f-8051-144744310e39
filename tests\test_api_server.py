"""
FastAPI服务器测试脚本
"""
import asyncio
import json
import pytest
import time
from fastapi.testclient import TestClient
from ..server.api_server import app
from models import ProcessingResult
from datetime import datetime


# 创建测试客户端
client = TestClient(app)


def test_root_endpoint():
    """测试根路径端点"""
    response = client.get("/")
    assert response.status_code == 200
    data = response.json()
    assert "message" in data
    assert "version" in data


def test_health_check():
    """测试健康检查端点"""
    response = client.get("/health")
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "healthy"
    assert "timestamp" in data
    assert "active_sessions" in data


def test_get_processor_types():
    """测试获取处理器类型端点"""
    response = client.get("/api/processors")
    assert response.status_code == 200
    data = response.json()
    assert "available_types" in data
    assert isinstance(data["available_types"], list)
    assert len(data["available_types"]) > 0


def test_create_session_success():
    """测试成功创建会话"""
    request_data = {"analysis_type": "opencv_preview"}
    response = client.post("/api/session", json=request_data)
    assert response.status_code == 200
    data = response.json()
    assert "session_key" in data
    assert data["analysis_type"] == "opencv_preview"
    assert "created_at" in data
    
    # 返回session_key用于后续测试
    return data["session_key"]


def test_create_session_invalid_type():
    """测试使用无效处理器类型创建会话"""
    request_data = {"analysis_type": "invalid_type"}
    response = client.post("/api/session", json=request_data)
    assert response.status_code == 400
    assert "Unsupported analysis_type" in str(response.json()["detail"])


def test_get_session_status():
    """测试获取会话状态"""
    # 先创建一个会话
    session_key = test_create_session_success()
    
    # 获取会话状态
    response = client.get(f"/api/session/{session_key}")
    assert response.status_code == 200
    data = response.json()
    assert data["session_key"] == session_key
    assert data["analysis_type"] == "opencv_preview"
    assert "status" in data
    assert "created_at" in data
    assert "last_activity" in data
    assert "processing_active" in data


def test_get_session_status_not_found():
    """测试获取不存在会话的状态"""
    response = client.get("/api/session/nonexistent-session")
    assert response.status_code == 404
    assert "Session not found" in str(response.json()["detail"])


def test_list_sessions():
    """测试列出所有会话"""
    # 先创建一个会话
    session_key = test_create_session_success()
    
    # 列出所有会话
    response = client.get("/api/sessions")
    assert response.status_code == 200
    data = response.json()
    assert isinstance(data, list)
    assert len(data) > 0
    
    # 检查创建的会话是否在列表中
    session_found = False
    for session in data:
        if session["session_key"] == session_key:
            session_found = True
            break
    assert session_found


def test_delete_session():
    """测试删除会话"""
    # 先创建一个会话
    session_key = test_create_session_success()
    
    # 删除会话
    response = client.delete(f"/api/session/{session_key}")
    assert response.status_code == 200
    assert "deleted successfully" in response.json()["message"]
    
    # 验证会话已被删除
    response = client.get(f"/api/session/{session_key}")
    assert response.status_code == 404


def test_delete_session_not_found():
    """测试删除不存在的会话"""
    response = client.delete("/api/session/nonexistent-session")
    assert response.status_code == 404
    assert "Session not found" in str(response.json()["detail"])


def test_create_session_missing_analysis_type():
    """测试缺少analysis_type参数的请求"""
    response = client.post("/api/session", json={})
    assert response.status_code == 422  # Validation error


def test_sse_events_not_found():
    """测试SSE连接到不存在的会话"""
    with client.stream("GET", "/events/nonexistent-session") as response:
        assert response.status_code == 404
        # For streaming responses, we need to read the content first
        content = response.read()
        response_data = json.loads(content)
        assert "Session not found" in str(response_data["detail"])


def test_sse_events():
    """测试SSE事件流连接"""
    # 创建一个会话
    request_data = {"analysis_type": "dummy"}
    response = client.post("/api/session", json=request_data)
    assert response.status_code == 200
    session_key = response.json()["session_key"]
    
    try:
        # 获取会话实例
        from ..server.session_manager import session_manager
        session = session_manager.get_session(session_key)
        assert session is not None
        
        # 手动向消息队列添加一个测试结果
        async def add_test_result():
            result = ProcessingResult(
                session_key=session_key,
                timestamp=datetime.now(),
                frame_info={"width": 640, "height": 480},
                analysis_data={"test": "data"}
            )
            await session.message_queue.put_result(result)
        
        # 运行异步任务
        asyncio.run(add_test_result())
        
        # 连接SSE事件流并接收少量事件
        received_events = []
        event_count = 0
        max_events = 3  # 只接收少量事件以加快测试
        current_event_type = None
        
        with client.stream("GET", f"/events/{session_key}") as response:
            assert response.status_code == 200
            assert response.headers["content-type"].startswith("text/event-stream")
            
            # 读取事件流
            client_disconnected = False
            for line in response.iter_lines():
                if not line:
                    # Empty line marks the end of an event
                    continue
                    
                # line = line.decode("utf-8")
                if line.startswith("event:"):
                    current_event_type = line.split(":", 1)[1].strip()
                elif line.startswith("data:"):
                    data = line.split(":", 1)[1].strip()
                    if current_event_type:
                        received_events.append((current_event_type, data))
                        event_count += 1
                        
                        # 收到足够的事件后断开连接
                        if event_count >= max_events:
                            client_disconnected = True
                            break
            
            # 由于测试环境可能运行很快，我们可能不会收到足够的事件就结束了
            # 所以这里不断言client_disconnected
            
            # 检查是否收到了至少一个事件
            assert len(received_events) > 0
            
            # 检查是否收到了连接成功事件
            connected_events = [e for e in received_events if e[0] == "connected"]
            assert len(connected_events) > 0
    finally:
        # 清理：删除会话
        response = client.delete(f"/api/session/{session_key}")
        assert response.status_code == 200


def test_session_workflow():
    """测试完整的会话工作流程"""
    # 1. 获取可用的处理器类型
    response = client.get("/api/processors")
    assert response.status_code == 200
    available_types = response.json()["available_types"]
    assert len(available_types) > 0
    
    # 2. 创建会话
    analysis_type = available_types[0]  # 使用第一个可用类型
    request_data = {"analysis_type": analysis_type}
    response = client.post("/api/session", json=request_data)
    assert response.status_code == 200
    session_key = response.json()["session_key"]
    
    # 3. 检查会话状态
    response = client.get(f"/api/session/{session_key}")
    assert response.status_code == 200
    assert response.json()["analysis_type"] == analysis_type
    
    # 4. 列出会话（应该包含我们创建的会话）
    response = client.get("/api/sessions")
    assert response.status_code == 200
    sessions = response.json()
    session_keys = [s["session_key"] for s in sessions]
    assert session_key in session_keys
    
    # 5. 建立SSE连接（只测试连接成功，不接收事件）
    with client.stream("GET", f"/events/{session_key}") as response:
        assert response.status_code == 200
        assert response.headers["content-type"].startswith("text/event-stream")
        
        # 只读取少量数据确认连接成功
        event_found = False
        for i, line in enumerate(response.iter_lines()):
            if line:
                # line = line.decode("utf-8")
                if line.startswith("event: connected"):
                    event_found = True
                    break
            # 只读取前几行
            if i >= 5:
                break
                
        assert event_found, "Connected event not found in SSE stream"
    
    # 6. 删除会话
    response = client.delete(f"/api/session/{session_key}")
    assert response.status_code == 200
    
    # 7. 验证会话已删除
    response = client.get(f"/api/session/{session_key}")
    assert response.status_code == 404


if __name__ == "__main__":
    # 运行所有测试
    print("Running FastAPI tests...")
    
    test_root_endpoint()
    print("✓ Root endpoint test passed")
    
    test_health_check()
    print("✓ Health check test passed")
    
    test_get_processor_types()
    print("✓ Processor types test passed")
    
    test_create_session_invalid_type()
    print("✓ Invalid session type test passed")
    
    test_get_session_status_not_found()
    print("✓ Session not found test passed")
    
    test_delete_session_not_found()
    print("✓ Delete non-existent session test passed")
    
    test_create_session_missing_analysis_type()
    print("✓ Missing analysis type test passed")
    
    test_sse_events_not_found()
    print("✓ SSE events not found test passed")
    
    test_sse_events()
    print("✓ SSE events test passed")
    
    test_session_workflow()
    print("✓ Complete session workflow test passed")
    
    print("\nAll tests passed! ✅")