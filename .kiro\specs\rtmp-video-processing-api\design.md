# 设计文档

## 概述

本系统基于现有的RTMP服务器实现一个实时视频流处理系统，通过FastAPI提供REST API和Server-Sent Events (SSE)，实现客户端视频流的实时处理和结果返回。系统采用会话管理机制，支持多个并发的视频处理任务，每个任务可以配置不同的处理器类型。

核心设计理念：
- **会话驱动**：通过会话管理实现资源隔离和生命周期管理
- **异步处理**：使用队列机制实现RTMP流处理和SSE结果推送的解耦
- **可扩展性**：支持多种视频处理器的动态注册和使用
- **实时性**：通过SSE实现低延迟的结果推送

## 架构

### 系统架构图

```
┌─────────────────┐    RTMP Stream     ┌─────────────────┐
│   Client App    │ ──────────────────→ │   RTMP Server   │
│                 │                     │                 │
└─────────────────┘                     └─────────────────┘
         │                                       │
         │ REST API                              │ Frame Data
         │                                       ▼
         ▼                               ┌─────────────────┐
┌─────────────────┐                     │ Video Processor │
│  FastAPI Server │                     │    Manager      │
│                 │                     └─────────────────┘
└─────────────────┘                             │
         │                                      │ Results
         │ SSE Connection                       ▼
         │                               ┌─────────────────┐
         └──────────────────────────────→│ Message Queue   │
                                         │   (per session) │
                                         └─────────────────┘
```

### 核心组件交互流程

1. **会话创建**：客户端通过REST API创建处理会话
2. **RTMP连接**：客户端使用会话密钥作为流密钥建立RTMP连接
3. **SSE连接**：客户端建立SSE连接接收处理结果
4. **视频处理**：RTMP服务器接收帧数据，调用处理器分析
5. **结果推送**：处理结果通过队列传递给SSE连接

## 组件和接口

### 1. 会话管理器 (SessionManager)

**职责**：管理所有活跃会话的生命周期和状态

```python
class SessionManager:
    def create_session(self, analysis_type: str) -> str
    def get_session(self, session_key: str) -> Optional[Session]
    def remove_session(self, session_key: str) -> bool
    def cleanup_expired_sessions(self) -> None
```

**设计决策**：
- 使用UUID作为会话密钥，确保唯一性和安全性
- 采用内存存储，适合实时处理场景的性能要求
- 支持会话超时机制，防止资源泄漏

### 2. 视频处理器接口 (VideoProcessor)

**职责**：定义统一的视频处理接口，支持多种处理器实现

```python
class VideoProcessor(ABC):
    @abstractmethod
    def initialize(self) -> None
        """初始化处理器资源"""
    
    @abstractmethod
    def process_frame(self, frame: np.ndarray) -> Dict[str, Any]
        """同步阻塞处理单帧，返回分析结果"""
    
    @abstractmethod
    def cleanup(self) -> None
        """清理处理器资源"""
```

**设计决策**：
- 使用抽象基类确保接口一致性
- process_frame设计为同步阻塞方法，简化子类实现
- 返回字典格式便于JSON序列化和扩展
- 包含initialize和cleanup方法确保资源正确管理

### 3. 处理器工厂 (ProcessorFactory)

**职责**：根据类型创建相应的处理器实例

```python
class ProcessorFactory:
    @staticmethod
    def create_processor(analysis_type: str) -> VideoProcessor
    
    @staticmethod
    def register_processor(name: str, processor_class: Type[VideoProcessor])
```

**设计决策**：
- 使用工厂模式支持处理器的动态注册
- 支持运行时添加新的处理器类型
- 提供类型验证和错误处理

### 4. 消息队列系统 (MessageQueue)

**职责**：为每个会话提供独立的消息队列

```python
class SessionQueue:
    def put_result(self, result: Dict[str, Any]) -> None
    def get_result(self, timeout: float = None) -> Optional[Dict[str, Any]]
    def close(self) -> None
```

**设计决策**：
- 每个会话独立队列，避免数据混淆
- 支持非阻塞和超时获取，适应SSE推送需求
- 使用asyncio.Queue实现异步操作

### 5. FastAPI REST接口

#### POST /api/session
- **输入**：`{"analysis_type": "opencv_preview"}`
- **输出**：`{"session_key": "uuid-string"}`
- **功能**：创建新的视频处理会话

#### GET /events/{session_key}
- **输出**：SSE事件流
- **功能**：建立实时结果推送连接

**设计决策**：
- 使用RESTful设计原则
- SSE选择基于其简单性和浏览器原生支持
- 支持CORS以便前端集成

### 6. 视频处理管理器 (VideoProcessingManager)

**职责**：管理RTMP回调和会话协调，但不直接管理帧队列和处理协程

```python
class VideoProcessingManager:
    def on_stream_start(self, stream_key: str) -> bool
        """RTMP流开始回调，验证会话并启动Session的处理协程"""
    
    def on_video_message(self, stream_key: str, frame: np.ndarray) -> None
        """RTMP帧接收回调，将帧放入对应Session的帧队列"""
    
    def on_stream_end(self, stream_key: str) -> None
        """RTMP流结束回调，处理连接状态判断"""
    
    def _should_end_session(self, session_key: str) -> bool
        """判断是否应该结束会话（区分连接不稳定和真正结束）"""
```

**设计决策**：
- **职责分离**：VideoProcessingManager只负责RTMP回调和会话协调
- **Session自治**：每个Session自己管理frame_queue和processing_task
- **异步处理**：on_video_message仅负责将帧放入Session的队列，避免阻塞RTMP接收
- **连接稳定性判断**：短暂的推流中断不立即结束会话，支持网络不稳定场景

### 7. 帧处理队列系统

**职责**：为每个会话提供独立的帧处理队列

```python
class FrameQueue:
    def put_frame(self, frame: np.ndarray) -> None
        """非阻塞放入帧数据"""
    
    async def get_frame(self, timeout: float = None) -> Optional[np.ndarray]
        """异步获取帧数据"""
    
    def close(self) -> None
        """关闭队列"""
```

**设计决策**：
- 使用asyncio.Queue实现异步队列操作
- 支持队列大小限制，防止内存溢出
- 提供超时机制，避免处理协程阻塞

## 数据模型

### Session 数据模型

```python
@dataclass
class Session:
    session_key: str
    analysis_type: str
    processor: VideoProcessor
    message_queue: SessionQueue
    frame_queue: FrameQueue
    processing_task: Optional[asyncio.Task]
    created_at: datetime
    last_activity: datetime
    status: SessionStatus
    
    def start_processing(self) -> None
        """启动帧处理协程"""
    
    def stop_processing(self) -> None
        """停止帧处理协程"""
    
    async def _process_frames(self) -> None
        """帧处理协程的主循环"""
```

### 处理结果数据模型

```python
@dataclass
class ProcessingResult:
    session_key: str
    timestamp: datetime
    frame_info: Dict[str, Any]  # 帧尺寸、格式等
    analysis_data: Dict[str, Any]  # 具体分析结果
    error: Optional[str] = None
```

**设计决策**：
- 使用dataclass提高代码可读性和类型安全
- 包含时间戳支持结果排序和调试
- 错误信息可选，支持部分失败场景

## 错误处理

### 错误分类和处理策略

1. **API错误**
   - 参数验证错误：返回400 Bad Request
   - 会话不存在：返回404 Not Found
   - 服务器内部错误：返回500 Internal Server Error

2. **RTMP连接错误**
   - 无效stream_key：拒绝连接并记录日志
   - 处理器异常：记录错误但继续处理后续帧
   - 连接中断：清理会话资源

3. **SSE连接错误**
   - 客户端断开：优雅关闭连接
   - 队列异常：记录错误并尝试重连

4. **视频处理错误**
   - 帧数据异常：跳过当前帧，记录警告
   - 处理器崩溃：重启处理器或降级处理

**设计决策**：
- 采用分层错误处理，每层负责相应的错误类型
- 优先保证系统稳定性，允许部分功能降级
- 详细的错误日志支持问题诊断

## 测试策略

### 单元测试

1. **SessionManager测试**
   - 会话创建和检索
   - 会话超时清理
   - 并发访问安全性

2. **VideoProcessor测试**
   - 各种处理器的功能测试
   - 异常情况处理
   - 资源清理验证

3. **MessageQueue测试**
   - 队列操作的正确性
   - 并发读写安全性
   - 内存泄漏检测

### 集成测试

1. **API集成测试**
   - 完整的会话创建到结果接收流程
   - 错误场景的端到端测试
   - 并发会话处理能力

2. **RTMP集成测试**
   - 用ffmpeg实现RTMP流输入
   - 验证帧数据处理流程
   - 连接异常恢复测试

### 性能测试

1. **并发处理能力**
   - 多会话同时处理的性能表现
   - 内存和CPU使用情况监控
   - 队列积压情况测试

2. **延迟测试**
   - 从帧接收到结果推送的端到端延迟
   - SSE推送的实时性验证

**设计决策**：
- 采用pytest框架，支持异步测试
- 使用模拟对象隔离外部依赖
- 包含性能基准测试，确保系统可扩展性

## 部署和配置

### 配置管理

```python
class Config:
    # RTMP服务器配置
    RTMP_HOST: str = "localhost"
    RTMP_PORT: int = 1935
    
    # FastAPI配置
    API_HOST: str = "0.0.0.0"
    API_PORT: int = 8000
    
    # 会话管理配置
    SESSION_TIMEOUT: int = 3600  # 秒
    MAX_CONCURRENT_SESSIONS: int = 100
    
    # 队列配置
    QUEUE_MAX_SIZE: int = 1000
    QUEUE_TIMEOUT: float = 1.0
```

需要支持从toml中读取配置。可以使用pydantic进行配置验证和类型转换。

### 日志配置

- 使用结构化日志格式（JSON）
- 包含会话ID、操作类型、时间戳等关键信息
- 支持不同级别的日志输出（DEBUG、INFO、WARNING、ERROR）

**设计决策**：
- 使用环境变量支持不同部署环境
- 配置验证确保系统启动时参数正确
- 支持热重载配置以便运维调整