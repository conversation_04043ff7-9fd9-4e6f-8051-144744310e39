"""
测试帧处理队列系统
"""
import asyncio
import pytest
import numpy as np
from ..models import FrameQueue


class TestFrameQueue:
    """测试FrameQueue类"""
    
    def test_queue_creation(self):
        """测试队列创建"""
        queue = FrameQueue(max_size=50)
        assert not queue.is_closed
        assert queue._queue.maxsize == 50
    
    def test_put_frame_sync(self):
        """测试同步放入帧数据"""
        queue = FrameQueue(max_size=10)
        
        # 创建测试帧
        frame = np.zeros((480, 640, 3), dtype=np.uint8)
        
        # 放入帧数据（同步操作）
        queue.put_frame(frame)
        
        # 验证队列不为空
        assert queue._queue.qsize() == 1
    
    @pytest.mark.asyncio
    async def test_get_frame_async(self):
        """测试异步获取帧数据"""
        queue = FrameQueue(max_size=10)
        
        # 创建测试帧
        original_frame = np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)
        
        # 放入帧数据
        queue.put_frame(original_frame)
        
        # 异步获取帧数据
        retrieved_frame = await queue.get_frame()
        
        assert retrieved_frame is not None
        assert np.array_equal(retrieved_frame, original_frame)
        assert retrieved_frame.shape == (480, 640, 3)
    
    @pytest.mark.asyncio
    async def test_get_frame_with_timeout(self):
        """测试带超时的获取帧数据"""
        queue = FrameQueue(max_size=10)
        
        # 测试超时获取（队列为空）
        frame = await queue.get_frame(timeout=0.1)
        assert frame is None
    
    def test_queue_full_behavior(self):
        """测试队列满时的行为"""
        queue = FrameQueue(max_size=2)
        
        # 创建测试帧
        frame1 = np.ones((100, 100, 3), dtype=np.uint8) * 1
        frame2 = np.ones((100, 100, 3), dtype=np.uint8) * 2
        frame3 = np.ones((100, 100, 3), dtype=np.uint8) * 3
        
        # 放入帧数据直到队列满
        queue.put_frame(frame1)
        queue.put_frame(frame2)
        queue.put_frame(frame3)  # 这应该会丢弃最旧的帧
        
        # 验证队列大小仍为2
        assert queue._queue.qsize() == 2
    
    @pytest.mark.asyncio
    async def test_queue_full_fifo_behavior(self):
        """测试队列满时的FIFO行为"""
        queue = FrameQueue(max_size=2)
        
        # 创建可区分的测试帧
        frame1 = np.ones((10, 10, 1), dtype=np.uint8) * 100
        frame2 = np.ones((10, 10, 1), dtype=np.uint8) * 150
        frame3 = np.ones((10, 10, 1), dtype=np.uint8) * 200
        
        # 放入帧数据
        queue.put_frame(frame1)
        queue.put_frame(frame2)
        queue.put_frame(frame3)  # 应该丢弃frame1
        
        # 获取帧数据，应该是frame2和frame3
        retrieved1 = await queue.get_frame()
        retrieved2 = await queue.get_frame()
        
        # 验证最旧的帧被丢弃了
        assert np.array_equal(retrieved1, frame2)
        assert np.array_equal(retrieved2, frame3)
    
    def test_queue_close(self):
        """测试队列关闭"""
        queue = FrameQueue(max_size=10)
        
        # 关闭队列
        queue.close()
        assert queue.is_closed
        
        # 关闭后不能放入帧
        frame = np.zeros((100, 100, 3), dtype=np.uint8)
        queue.put_frame(frame)  # 应该被忽略
        
        assert queue._queue.qsize() == 0
    
    @pytest.mark.asyncio
    async def test_get_frame_after_close(self):
        """测试关闭后获取帧"""
        queue = FrameQueue(max_size=10)
        
        # 关闭队列
        queue.close()
        
        # 关闭后获取帧返回None
        frame = await queue.get_frame()
        assert frame is None
    
    @pytest.mark.asyncio
    async def test_concurrent_put_get(self):
        """测试并发放入和获取操作"""
        queue = FrameQueue(max_size=50)
        frames_put = []
        frames_got = []
        
        async def producer():
            """生产者协程"""
            for i in range(20):
                frame = np.ones((50, 50, 1), dtype=np.uint8) * (i + 1)
                frames_put.append(frame)
                queue.put_frame(frame)
                await asyncio.sleep(0.01)  # 小延迟模拟实际情况
        
        async def consumer():
            """消费者协程"""
            for _ in range(20):
                frame = await queue.get_frame(timeout=1.0)
                if frame is not None:
                    frames_got.append(frame)
        
        # 并发运行生产者和消费者
        await asyncio.gather(producer(), consumer())
        
        # 验证所有帧都被正确处理
        assert len(frames_got) == 20
        assert len(frames_put) == 20
        
        # 验证帧的顺序和内容
        for i, (put_frame, got_frame) in enumerate(zip(frames_put, frames_got)):
            assert np.array_equal(put_frame, got_frame)
    
    @pytest.mark.asyncio
    async def test_memory_management_with_large_frames(self):
        """测试大帧数据的内存管理"""
        queue = FrameQueue(max_size=3)
        
        # 创建大帧数据来测试内存管理
        large_frames = []
        for i in range(10):
            # 创建较大的帧（模拟高分辨率视频）
            frame = np.random.randint(0, 255, (1080, 1920, 3), dtype=np.uint8)
            large_frames.append(frame)
            queue.put_frame(frame)
        
        # 队列应该只保留最新的3帧
        retrieved_frames = []
        while True:
            frame = await queue.get_frame(timeout=0.1)
            if frame is None:
                break
            retrieved_frames.append(frame)
        
        # 验证只有最新的3帧
        assert len(retrieved_frames) == 3
        
        # 验证是最新的3帧
        for i, retrieved_frame in enumerate(retrieved_frames):
            expected_frame = large_frames[7 + i]  # 最后3帧：索引7,8,9
            assert np.array_equal(retrieved_frame, expected_frame)
    
    def test_different_frame_formats(self):
        """测试不同格式的帧数据"""
        queue = FrameQueue(max_size=10)
        
        # 测试不同格式的帧
        formats = [
            (480, 640, 3),      # RGB
            (480, 640, 1),      # 灰度
            (720, 1280, 3),     # HD
            (1080, 1920, 3),    # Full HD
        ]
        
        for shape in formats:
            frame = np.random.randint(0, 255, shape, dtype=np.uint8)
            queue.put_frame(frame)
        
        # 验证所有格式都能正确存储
        assert queue._queue.qsize() == len(formats)


if __name__ == "__main__":
    # 运行测试
    asyncio.run(pytest.main([__file__, "-v"]))