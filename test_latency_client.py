#!/usr/bin/env python3
"""
延迟测试客户端的简单测试脚本
用于验证基本功能是否正常
"""

import asyncio
import sys
import time

# 检查依赖
try:
    import cv2
    import matplotlib.pyplot as plt
    import numpy as np
    import httpx
    import requests
    import sseclient
    print("✓ 所有依赖库已安装")
except ImportError as e:
    print(f"✗ 缺少依赖库: {e}")
    print("请运行: pip install opencv-python matplotlib numpy httpx requests sseclient-py")
    sys.exit(1)

from latency_test_client import LatencyTestClient


async def test_basic_functionality():
    """测试基本功能"""
    print("=" * 50)
    print("延迟测试客户端基本功能测试")
    print("=" * 50)
    
    client = LatencyTestClient()
    
    try:
        # 1. 测试摄像头检测
        print("\n1. 测试摄像头检测...")
        try:
            width, height, fps = client.detect_camera_info()
            print(f"✓ 摄像头检测成功: {width}x{height} @ {fps}fps")
        except Exception as e:
            print(f"✗ 摄像头检测失败: {e}")
            print("请确保摄像头已连接且未被其他程序占用")
            return False
        
        # 2. 测试API连接
        print("\n2. 测试API服务器连接...")
        try:
            async with httpx.AsyncClient() as http_client:
                response = await http_client.get(f"{client.api_base_url}/health", timeout=5.0)
                if response.status_code == 200:
                    print("✓ API服务器连接正常")
                else:
                    print(f"✗ API服务器响应异常: {response.status_code}")
                    return False
        except Exception as e:
            print(f"✗ 无法连接到API服务器: {e}")
            print("请确保服务器已启动 (python -m server.main)")
            return False
        
        # 3. 测试会话创建
        print("\n3. 测试会话创建...")
        try:
            session_key = await client.create_session("opencv_preview")
            print(f"✓ 会话创建成功: {session_key}")
        except Exception as e:
            print(f"✗ 会话创建失败: {e}")
            return False
        
        # 4. 测试SSE连接
        print("\n4. 测试SSE连接...")
        try:
            sse_thread = client.start_sse_connection()
            time.sleep(3)  # 等待连接建立
            print("✓ SSE连接建立成功")
        except Exception as e:
            print(f"✗ SSE连接失败: {e}")
            return False
        
        # 5. 测试图表功能
        print("\n5. 测试图表功能...")
        try:
            # 模拟一些延迟数据
            client.latency_window.extend([50, 45, 60, 55, 48])
            client.frame_window.extend([1, 2, 3, 4, 5])
            
            client.setup_realtime_plot()
            print("✓ 图表功能正常")
            
            # 关闭图表
            plt.close('all')
            
        except Exception as e:
            print(f"✗ 图表功能异常: {e}")
            return False
        
        # 6. 清理资源
        print("\n6. 清理资源...")
        client.cleanup()
        await client.delete_session()
        print("✓ 资源清理完成")
        
        print("\n" + "=" * 50)
        print("✓ 所有基本功能测试通过！")
        print("可以运行完整的延迟测试了")
        print("=" * 50)
        
        return True
        
    except Exception as e:
        print(f"\n✗ 测试过程中发生错误: {e}")
        client.cleanup()
        if client.session_key:
            await client.delete_session()
        return False


def test_ffmpeg_camera():
    """测试FFmpeg摄像头访问"""
    print("\n" + "=" * 50)
    print("FFmpeg摄像头访问测试")
    print("=" * 50)
    
    import subprocess
    
    # 列出可用设备
    print("1. 列出可用的摄像头设备...")
    try:
        cmd = ["ffmpeg", "-f", "dshow", "-list_devices", "true", "-i", "dummy"]
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=10, encoding='utf-8', errors='ignore')
        
        print("FFmpeg输出:")
        print(result.stderr)
        
        # 从输出中提取视频设备名称
        lines = result.stderr.split('\n')
        video_devices = []
        for line in lines:
            if '"' in line and 'video' in line.lower():
                # 提取设备名称
                start = line.find('"') + 1
                end = line.find('"', start)
                if start > 0 and end > start:
                    device_name = line[start:end]
                    video_devices.append(device_name)
        
        if video_devices:
            print(f"\n找到 {len(video_devices)} 个视频设备:")
            for i, device in enumerate(video_devices):
                print(f"  {i+1}. {device}")
            
            # 测试第一个设备
            print(f"\n2. 测试设备: {video_devices[0]}")
            test_cmd = [
                "ffmpeg",
                "-f", "dshow",
                "-i", f"video={video_devices[0]}",
                "-t", "2",  # 只录制2秒
                "-f", "null",
                "-"
            ]
            
            print("运行测试命令...")
            test_result = subprocess.run(test_cmd, capture_output=True, text=True, timeout=15, encoding='utf-8', errors='ignore')
            
            if test_result.returncode == 0:
                print("✓ 摄像头访问测试成功！")
                return video_devices[0]
            else:
                print(f"✗ 摄像头访问测试失败:")
                print(test_result.stderr)
                return None
        else:
            print("✗ 未找到可用的视频设备")
            return None
            
    except subprocess.TimeoutExpired:
        print("✗ FFmpeg命令超时")
        return None
    except FileNotFoundError:
        print("✗ 未找到FFmpeg，请确保已安装并添加到PATH")
        return None
    except Exception as e:
        print(f"✗ FFmpeg测试失败: {e}")
        return None


async def main():
    """主测试函数"""
    print("延迟测试客户端验证程序")
    print("此程序将验证延迟测试客户端的各项功能")
    
    # 测试FFmpeg摄像头访问
    camera_device = test_ffmpeg_camera()
    
    if camera_device:
        print(f"\n建议在latency_test_client.py中使用摄像头: {camera_device}")
    
    # 测试基本功能
    success = await test_basic_functionality()
    
    if success:
        print("\n🎉 验证完成！可以运行延迟测试了:")
        print("python latency_test_client.py")
    else:
        print("\n❌ 验证失败，请检查上述错误信息")
        
    return success


if __name__ == "__main__":
    asyncio.run(main())
