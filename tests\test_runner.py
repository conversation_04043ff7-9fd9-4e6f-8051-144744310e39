#!/usr/bin/env python3
"""
简单的集成测试验证器
用于快速验证基本功能是否正常工作
"""
import asyncio
import httpx
import time
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from main import ApplicationManager
from config import load_config
from server.session_manager import ProcessorFactory
from processors.processors import DummyProcessor


class SimpleTestRunner:
    """简单的测试运行器"""
    
    def __init__(self):
        self.app_manager = None
        self.base_url = None
        
    async def setup(self):
        """设置测试环境"""
        print("Setting up test environment...")
        
        # 注册测试处理器
        ProcessorFactory.register_processor("dummy", DummyProcessor)
        
        # 加载测试配置
        config_path = "tests/test_config.toml"
        if not os.path.exists(config_path):
            config_path = "config.toml"
        
        config = load_config(config_path, use_env=False)
        
        # 创建应用管理器
        self.app_manager = ApplicationManager()
        self.app_manager.config = config
        
        # 初始化日志系统
        self.app_manager.init_logging()
        
        # 创建组件
        self.app_manager.create_components()
        
        # 启动服务
        await self.app_manager.start_services()
        
        # 等待服务启动
        await asyncio.sleep(3)
        
        self.base_url = f"http://{config.fastapi.host}:{config.fastapi.port}"
        print(f"Services started at {self.base_url}")
        
    async def cleanup(self):
        """清理测试环境"""
        if self.app_manager:
            print("Cleaning up test environment...")
            await self.app_manager.shutdown_services()
            
    async def test_health_check(self):
        """测试健康检查端点"""
        print("Testing health check...")
        
        async with httpx.AsyncClient(base_url=self.base_url, timeout=10.0) as client:
            response = await client.get("/health")
            
            if response.status_code == 200:
                data = response.json()
                print(f"✓ Health check passed: {data['status']}")
                return True
            else:
                print(f"✗ Health check failed: {response.status_code}")
                return False
                
    async def test_session_creation(self):
        """测试会话创建"""
        print("Testing session creation...")
        
        async with httpx.AsyncClient(base_url=self.base_url, timeout=10.0) as client:
            # 创建会话
            response = await client.post("/api/session", json={"analysis_type": "dummy"})
            
            if response.status_code == 200:
                data = response.json()
                session_key = data["session_key"]
                print(f"✓ Session created: {session_key}")
                
                # 查询会话状态
                response = await client.get(f"/api/session/{session_key}")
                if response.status_code == 200:
                    print("✓ Session status query successful")
                    
                    # 删除会话
                    response = await client.delete(f"/api/session/{session_key}")
                    if response.status_code == 200:
                        print("✓ Session deletion successful")
                        return True
                    else:
                        print(f"✗ Session deletion failed: {response.status_code}")
                        return False
                else:
                    print(f"✗ Session status query failed: {response.status_code}")
                    return False
            else:
                print(f"✗ Session creation failed: {response.status_code}")
                return False
                
    async def test_sse_connection(self):
        """测试SSE连接"""
        print("Testing SSE connection...")
        
        async with httpx.AsyncClient(base_url=self.base_url, timeout=10.0) as client:
            # 创建会话
            response = await client.post("/api/session", json={"analysis_type": "dummy"})
            
            if response.status_code != 200:
                print(f"✗ Failed to create session for SSE test: {response.status_code}")
                return False
                
            session_key = response.json()["session_key"]
            
            try:
                # 测试SSE连接
                sse_url = f"/events/{session_key}"
                
                # 使用stream方式测试SSE连接
                with client.stream('GET', sse_url, timeout=5.0) as response:
                    if response.status_code == 200:
                        # 读取第一个事件
                        for line in response.iter_lines():
                            if line.strip():
                                print(f"✓ SSE connection established, received: {line[:50]}...")
                                break
                        return True
                    else:
                        print(f"✗ SSE connection failed: {response.status_code}")
                        return False
                        
            except Exception as e:
                print(f"✗ SSE connection error: {e}")
                return False
            finally:
                # 清理会话
                try:
                    await client.delete(f"/api/session/{session_key}")
                except:
                    pass
                    
    async def test_processor_types(self):
        """测试处理器类型查询"""
        print("Testing processor types...")
        
        async with httpx.AsyncClient(base_url=self.base_url, timeout=10.0) as client:
            response = await client.get("/api/processors")
            
            if response.status_code == 200:
                data = response.json()
                types = data["available_types"]
                print(f"✓ Available processor types: {types}")
                return len(types) > 0
            else:
                print(f"✗ Processor types query failed: {response.status_code}")
                return False
                
    async def run_all_tests(self):
        """运行所有测试"""
        print("=" * 50)
        print("RTMP Video Processing API - Basic Function Test")
        print("=" * 50)
        
        try:
            await self.setup()
            
            tests = [
                ("Health Check", self.test_health_check),
                ("Processor Types", self.test_processor_types),
                ("Session Creation", self.test_session_creation),
                ("SSE Connection", self.test_sse_connection),
            ]
            
            results = []
            
            for test_name, test_func in tests:
                print(f"\n--- {test_name} ---")
                try:
                    result = await test_func()
                    results.append((test_name, result))
                except Exception as e:
                    print(f"✗ {test_name} failed with exception: {e}")
                    results.append((test_name, False))
                    
            # 总结结果
            print("\n" + "=" * 50)
            print("Test Results Summary:")
            print("=" * 50)
            
            passed = 0
            total = len(results)
            
            for test_name, result in results:
                status = "PASS" if result else "FAIL"
                print(f"{test_name}: {status}")
                if result:
                    passed += 1
                    
            print(f"\nTotal: {passed}/{total} tests passed")
            
            if passed == total:
                print("🎉 All basic tests passed!")
                return True
            else:
                print("❌ Some tests failed!")
                return False
                
        except Exception as e:
            print(f"Test setup failed: {e}")
            return False
        finally:
            await self.cleanup()


async def main():
    """主函数"""
    runner = SimpleTestRunner()
    success = await runner.run_all_tests()
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\nTests interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"Fatal error: {e}")
        sys.exit(1)