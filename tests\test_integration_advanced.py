"""
高级集成测试
包含压力测试、错误恢复测试和边界条件测试
"""
import asyncio
import pytest
import httpx
import subprocess
import time
import threading
import json
import os
import psutil
from typing import List, Dict, Any, Optional
from unittest.mock import patch, MagicMock
import numpy as np

from .test_integration import FFmpegRTMPStreamer, SSEClient, TestVideoProcessor, app_manager, test_config, http_client
from server.session_manager import ProcessorFactory
from processors.processors import OpenCVPreviewProcessor


class StressTestProcessor(TestVideoProcessor):
    """压力测试处理器，支持配置处理延迟和错误率"""
    
    def __init__(self, processing_delay: float = 0.0, error_rate: float = 0.0):
        super().__init__()
        self.processing_delay = processing_delay
        self.error_rate = error_rate
        self.error_count = 0
        
    def process_frame(self, frame: np.ndarray) -> Dict[str, Any]:
        """处理视频帧，支持延迟和错误注入"""
        # 模拟处理延迟
        if self.processing_delay > 0:
            time.sleep(self.processing_delay)
            
        # 模拟处理错误
        if self.error_rate > 0 and np.random.random() < self.error_rate:
            self.error_count += 1
            raise RuntimeError(f"Simulated processing error #{self.error_count}")
            
        result = super().process_frame(frame)
        result.update({
            'processing_delay': self.processing_delay,
            'error_rate': self.error_rate,
            'error_count': self.error_count
        })
        
        return result


@pytest.fixture(scope="session")
def stress_test_config():
    """压力测试配置"""
    # 注册压力测试处理器
    ProcessorFactory.register_processor("stress_test", lambda: StressTestProcessor(0.1, 0.05))
    ProcessorFactory.register_processor("slow_processor", lambda: StressTestProcessor(0.5, 0.0))
    ProcessorFactory.register_processor("error_processor", lambda: StressTestProcessor(0.0, 0.3))


@pytest.mark.integration
@pytest.mark.slow
class TestStressAndPerformance:
    """压力测试和性能测试"""
    
    @pytest.mark.asyncio
    async def test_high_concurrency_sessions(self, app_manager, http_client, test_config, stress_test_config):
        """测试高并发会话处理能力"""
        num_sessions = 5  # 适中的并发数，避免测试环境过载
        sessions = []
        sse_clients = []
        streamers = []
        
        # 记录初始系统资源
        process = psutil.Process()
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        initial_cpu_percent = process.cpu_percent()
        
        try:
            # 创建多个会话
            for i in range(num_sessions):
                response = await http_client.post("/api/session", json={"analysis_type": "stress_test"})
                assert response.status_code == 200
                
                session_key = response.json()["session_key"]
                sessions.append(session_key)
                
                # 建立SSE连接
                sse_url = f"http://{test_config.fastapi.host}:{test_config.fastapi.port}/events/{session_key}"
                sse_client = SSEClient(sse_url)
                sse_client.start()
                sse_clients.append(sse_client)
            
            # 等待SSE连接建立
            await asyncio.sleep(2)
            
            # 并发开始RTMP推流
            for session_key in sessions:
                rtmp_url = f"rtmp://{test_config.rtmp.host}:{test_config.rtmp.port}/live/{session_key}"
                streamer = FFmpegRTMPStreamer(rtmp_url, duration=6, fps=2)
                if streamer.start_streaming():
                    streamers.append(streamer)
            
            if not streamers:
                pytest.skip("No FFmpeg streamers could be started")
            
            print(f"Started {len(streamers)} concurrent streams")
            
            # 监控系统资源使用
            max_memory = initial_memory
            max_cpu = initial_cpu_percent
            
            for _ in range(10):  # 监控10秒
                await asyncio.sleep(1)
                
                current_memory = process.memory_info().rss / 1024 / 1024
                current_cpu = process.cpu_percent()
                
                max_memory = max(max_memory, current_memory)
                max_cpu = max(max_cpu, current_cpu)
            
            # 停止所有推流
            for streamer in streamers:
                streamer.stop_streaming()
            
            # 等待处理完成
            await asyncio.sleep(2)
            
            # 验证所有会话都收到了事件
            total_events = 0
            for i, sse_client in enumerate(sse_clients):
                events = sse_client.events
                result_events = sse_client.get_events_by_type('result')
                
                assert len(events) >= 1, f"Session {i} should receive at least 1 event"
                total_events += len(result_events)
            
            print(f"Total events received: {total_events}")
            print(f"Memory usage: {initial_memory:.1f}MB -> {max_memory:.1f}MB (+{max_memory-initial_memory:.1f}MB)")
            print(f"CPU usage peak: {max_cpu:.1f}%")
            
            # 验证资源使用在合理范围内
            memory_increase = max_memory - initial_memory
            assert memory_increase < 200, f"Memory usage increased by {memory_increase:.1f}MB, which is too high"
            
        finally:
            # 清理所有资源
            for sse_client in sse_clients:
                sse_client.stop()
            
            for streamer in streamers:
                streamer.stop_streaming()
            
            for session_key in sessions:
                try:
                    await http_client.delete(f"/api/session/{session_key}")
                except:
                    pass
    
    @pytest.mark.asyncio
    async def test_long_running_session(self, app_manager, http_client, test_config, stress_test_config):
        """测试长时间运行的会话"""
        # 创建会话
        response = await http_client.post("/api/session", json={"analysis_type": "stress_test"})
        assert response.status_code == 200
        
        session_key = response.json()["session_key"]
        
        # 建立SSE连接
        sse_url = f"http://{test_config.fastapi.host}:{test_config.fastapi.port}/events/{session_key}"
        sse_client = SSEClient(sse_url)
        sse_client.start()
        
        await asyncio.sleep(1)
        
        try:
            rtmp_url = f"rtmp://{test_config.rtmp.host}:{test_config.rtmp.port}/live/{session_key}"
            
            # 长时间推流（10秒，低帧率）
            with FFmpegRTMPStreamer(rtmp_url, duration=10, fps=1) as streamer:
                if not streamer.is_running:
                    pytest.skip("FFmpeg streaming failed to start")
                
                # 监控会话状态
                for i in range(12):  # 检查12次，每次1秒
                    await asyncio.sleep(1)
                    
                    # 检查会话是否仍然活跃
                    response = await http_client.get(f"/api/session/{session_key}")
                    assert response.status_code == 200
                    
                    session_status = response.json()
                    assert session_status["status"] in ["created", "processing", "active"]
            
            # 验证收到了足够的事件
            result_events = sse_client.get_events_by_type('result')
            assert len(result_events) >= 5, f"Should receive at least 5 result events, got {len(result_events)}"
            
        finally:
            sse_client.stop()
            await http_client.delete(f"/api/session/{session_key}")
    
    @pytest.mark.asyncio
    async def test_processor_with_delays(self, app_manager, http_client, test_config, stress_test_config):
        """测试处理延迟对系统的影响"""
        # 创建使用慢处理器的会话
        response = await http_client.post("/api/session", json={"analysis_type": "slow_processor"})
        assert response.status_code == 200
        
        session_key = response.json()["session_key"]
        
        # 建立SSE连接
        sse_url = f"http://{test_config.fastapi.host}:{test_config.fastapi.port}/events/{session_key}"
        sse_client = SSEClient(sse_url)
        sse_client.start()
        
        await asyncio.sleep(1)
        
        try:
            rtmp_url = f"rtmp://{test_config.rtmp.host}:{test_config.rtmp.port}/live/{session_key}"
            
            start_time = time.time()
            
            with FFmpegRTMPStreamer(rtmp_url, duration=4, fps=2) as streamer:
                if not streamer.is_running:
                    pytest.skip("FFmpeg streaming failed to start")
                
                await asyncio.sleep(6)  # 等待处理完成
            
            end_time = time.time()
            processing_time = end_time - start_time
            
            # 验证处理时间合理（应该比正常处理时间长）
            assert processing_time >= 4, "Processing should take at least the stream duration"
            
            # 验证仍然收到了结果
            result_events = sse_client.get_events_by_type('result')
            assert len(result_events) >= 1, "Should receive at least 1 result event despite delays"
            
        finally:
            sse_client.stop()
            await http_client.delete(f"/api/session/{session_key}")


@pytest.mark.integration
class TestErrorRecovery:
    """错误恢复测试"""
    
    @pytest.mark.asyncio
    async def test_processor_error_handling(self, app_manager, http_client, test_config, stress_test_config):
        """测试处理器错误的处理和恢复"""
        # 创建使用错误处理器的会话
        response = await http_client.post("/api/session", json={"analysis_type": "error_processor"})
        assert response.status_code == 200
        
        session_key = response.json()["session_key"]
        
        # 建立SSE连接
        sse_url = f"http://{test_config.fastapi.host}:{test_config.fastapi.port}/events/{session_key}"
        sse_client = SSEClient(sse_url)
        sse_client.start()
        
        await asyncio.sleep(1)
        
        try:
            rtmp_url = f"rtmp://{test_config.rtmp.host}:{test_config.rtmp.port}/live/{session_key}"
            
            with FFmpegRTMPStreamer(rtmp_url, duration=5, fps=3) as streamer:
                if not streamer.is_running:
                    pytest.skip("FFmpeg streaming failed to start")
                
                await asyncio.sleep(7)
            
            # 验证系统仍然运行（尽管有错误）
            response = await http_client.get(f"/api/session/{session_key}")
            assert response.status_code == 200
            
            # 验证收到了一些成功的结果（错误率不是100%）
            result_events = sse_client.get_events_by_type('result')
            assert len(result_events) >= 1, "Should receive at least some successful results"
            
            # 可能会收到错误事件
            error_events = sse_client.get_events_by_type('error')
            print(f"Received {len(error_events)} error events and {len(result_events)} result events")
            
        finally:
            sse_client.stop()
            await http_client.delete(f"/api/session/{session_key}")
    
    @pytest.mark.asyncio
    async def test_multiple_rtmp_disconnections(self, app_manager, http_client, test_config):
        """测试多次RTMP连接断开和重连"""
        # 创建会话
        response = await http_client.post("/api/session", json={"analysis_type": "test"})
        assert response.status_code == 200
        
        session_key = response.json()["session_key"]
        
        # 建立SSE连接
        sse_url = f"http://{test_config.fastapi.host}:{test_config.fastapi.port}/events/{session_key}"
        sse_client = SSEClient(sse_url)
        sse_client.start()
        
        await asyncio.sleep(1)
        
        try:
            rtmp_url = f"rtmp://{test_config.rtmp.host}:{test_config.rtmp.port}/live/{session_key}"
            
            # 进行多次短暂的推流
            for i in range(3):
                print(f"Starting stream attempt {i+1}")
                
                with FFmpegRTMPStreamer(rtmp_url, duration=2, fps=2) as streamer:
                    if not streamer.is_running:
                        print(f"Stream {i+1} failed to start")
                        continue
                    
                    await asyncio.sleep(3)
                
                # 短暂等待
                await asyncio.sleep(1)
                
                # 验证会话仍然存在
                response = await http_client.get(f"/api/session/{session_key}")
                assert response.status_code == 200
            
            # 验证收到了来自多次连接的结果
            result_events = sse_client.get_events_by_type('result')
            assert len(result_events) >= 2, f"Should receive results from multiple connections, got {len(result_events)}"
            
        finally:
            sse_client.stop()
            await http_client.delete(f"/api/session/{session_key}")
    
    @pytest.mark.asyncio
    async def test_sse_reconnection_simulation(self, app_manager, http_client, test_config):
        """测试SSE重连场景"""
        # 创建会话
        response = await http_client.post("/api/session", json={"analysis_type": "test"})
        assert response.status_code == 200
        
        session_key = response.json()["session_key"]
        
        try:
            # 第一次SSE连接
            sse_url = f"http://{test_config.fastapi.host}:{test_config.fastapi.port}/events/{session_key}"
            sse_client1 = SSEClient(sse_url)
            sse_client1.start()
            
            await asyncio.sleep(1)
            
            # 开始推流
            rtmp_url = f"rtmp://{test_config.rtmp.host}:{test_config.rtmp.port}/live/{session_key}"
            
            with FFmpegRTMPStreamer(rtmp_url, duration=2, fps=2) as streamer:
                if not streamer.is_running:
                    pytest.skip("FFmpeg streaming failed to start")
                
                await asyncio.sleep(1)
                
                # 断开第一个SSE连接
                sse_client1.stop()
                
                # 立即建立第二个SSE连接
                sse_client2 = SSEClient(sse_url)
                sse_client2.start()
                
                await asyncio.sleep(2)
                
                # 验证第二个连接收到了事件
                events2 = sse_client2.events
                assert len(events2) >= 1, "Second SSE connection should receive events"
                
                sse_client2.stop()
            
            # 验证会话仍然正常
            response = await http_client.get(f"/api/session/{session_key}")
            assert response.status_code == 200
            
        finally:
            await http_client.delete(f"/api/session/{session_key}")


@pytest.mark.integration
class TestBoundaryConditions:
    """边界条件测试"""
    
    @pytest.mark.asyncio
    async def test_maximum_session_limit(self, app_manager, http_client, test_config):
        """测试最大会话数限制"""
        max_sessions = test_config.session.max_concurrent_sessions
        sessions = []
        
        print(f"Test config max_concurrent_sessions: {max_sessions}")
        
        # 验证session manager的配置
        from server.session_manager import session_manager
        current_config = session_manager.get_config()
        print(f"Session manager config: {current_config}")
        
        try:
            # 创建最大数量的会话
            for i in range(max_sessions):
                response = await http_client.post("/api/session", json={"analysis_type": "test"})
                assert response.status_code == 200, f"Failed to create session {i+1}/{max_sessions}"
                
                session_key = response.json()["session_key"]
                sessions.append(session_key)
                print(f"Created session {i+1}/{max_sessions}: {session_key}")
            
            # 尝试创建超出限制的会话
            print(f"Attempting to create session {max_sessions + 1} (should fail)")
            response = await http_client.post("/api/session", json={"analysis_type": "test"})
            print(f"Response status: {response.status_code}")
            if response.status_code != 400:
                print(f"Response body: {response.text}")
            assert response.status_code == 400, "Should reject session creation when limit is reached"
            
        finally:
            # 清理所有会话
            for session_key in sessions:
                try:
                    await http_client.delete(f"/api/session/{session_key}")
                except:
                    pass
    
    @pytest.mark.asyncio
    async def test_very_short_rtmp_streams(self, app_manager, http_client, test_config):
        """测试非常短的RTMP流"""
        # 创建会话
        response = await http_client.post("/api/session", json={"analysis_type": "test"})
        assert response.status_code == 200
        
        session_key = response.json()["session_key"]
        
        # 建立SSE连接
        sse_url = f"http://{test_config.fastapi.host}:{test_config.fastapi.port}/events/{session_key}"
        sse_client = SSEClient(sse_url)
        sse_client.start()
        
        await asyncio.sleep(1)
        
        try:
            rtmp_url = f"rtmp://{test_config.rtmp.host}:{test_config.rtmp.port}/live/{session_key}"
            
            # 非常短的推流（1秒）
            with FFmpegRTMPStreamer(rtmp_url, duration=1, fps=5) as streamer:
                if not streamer.is_running:
                    pytest.skip("FFmpeg streaming failed to start")
                
                await asyncio.sleep(2)
            
            # 验证系统正确处理了短流
            response = await http_client.get(f"/api/session/{session_key}")
            assert response.status_code == 200
            
            # 可能收到一些事件，但不强制要求
            events = sse_client.events
            print(f"Received {len(events)} events from very short stream")
            
        finally:
            sse_client.stop()
            await http_client.delete(f"/api/session/{session_key}")
    
    @pytest.mark.asyncio
    async def test_invalid_rtmp_stream_key(self, app_manager, http_client, test_config):
        """测试使用无效流密钥的RTMP连接"""
        invalid_session_key = "invalid-session-key-12345"
        rtmp_url = f"rtmp://{test_config.rtmp.host}:{test_config.rtmp.port}/live/{invalid_session_key}"
        
        # 尝试使用无效会话密钥推流
        with FFmpegRTMPStreamer(rtmp_url, duration=2, fps=2) as streamer:
            if not streamer.is_running:
                # 这是预期的行为 - 无效密钥应该导致连接失败
                print("✓ Invalid stream key correctly rejected")
                return
            
            # 如果连接成功了，等待一下看是否会被断开
            await asyncio.sleep(3)
        
        # 验证没有创建对应的会话
        response = await http_client.get(f"/api/session/{invalid_session_key}")
        assert response.status_code == 404, "Invalid session key should not exist"


if __name__ == "__main__":
    # 运行高级集成测试
    pytest.main([__file__, "-v", "-m", "integration"])