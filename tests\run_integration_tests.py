#!/usr/bin/env python3
"""
集成测试运行脚本
提供便捷的方式运行不同类型的集成测试
"""
import argparse
import subprocess
import sys
import os
import shutil
from pathlib import Path


def check_dependencies():
    """检查测试依赖"""
    print("Checking dependencies...")
    
    # 检查Python包
    required_packages = ['pytest', 'httpx', 'numpy', 'psutil']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✓ {package}")
        except ImportError:
            missing_packages.append(package)
            print(f"✗ {package} (missing)")
    
    if missing_packages:
        print(f"\nMissing packages: {', '.join(missing_packages)}")
        print("Install with: pip install " + " ".join(missing_packages))
        return False
    
    # 检查FFmpeg
    try:
        result = subprocess.run(['ffmpeg', '-version'], 
                              capture_output=True, check=True)
        print("✓ FFmpeg")
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("✗ FFmpeg (missing or not in PATH)")
        print("FFmpeg is required for RTMP streaming tests")
        print("Install from: https://ffmpeg.org/download.html")
        return False
    
    print("\nAll dependencies are available!")
    return True


def run_tests(test_type="basic", verbose=False, markers=None):
    """运行集成测试"""
    
    # 确保在正确的目录中
    script_dir = Path(__file__).parent
    os.chdir(script_dir.parent)
    
    # 构建pytest命令
    cmd = ["python", "-m", "pytest", "tests/test_integration.py"]
    
    # 添加标记
    if test_type == "basic":
        cmd.extend(["-m", "integration and not slow"])
    elif test_type == "full":
        cmd.extend(["-m", "integration"])
    elif test_type == "ffmpeg":
        cmd.extend(["-m", "integration", "-k", "ffmpeg or rtmp"])
    elif test_type == "concurrent":
        cmd.extend(["-m", "integration", "-k", "concurrent"])
    elif test_type == "error":
        cmd.extend(["-m", "integration", "-k", "error"])
    
    if markers:
        cmd.extend(["-m", markers])
    
    # 添加详细输出
    if verbose:
        cmd.extend(["-v", "-s"])
    else:
        cmd.append("-v")
    
    # 添加其他有用的选项
    cmd.extend([
        "--tb=short",  # 简短的traceback
        "--durations=10",  # 显示最慢的10个测试
    ])
    
    print(f"Running command: {' '.join(cmd)}")
    print("-" * 60)
    
    # 运行测试
    try:
        result = subprocess.run(cmd, check=False)
        return result.returncode == 0
    except KeyboardInterrupt:
        print("\nTests interrupted by user")
        return False


def main():
    parser = argparse.ArgumentParser(description="Run integration tests")
    
    parser.add_argument(
        "--type", 
        choices=["basic", "full", "ffmpeg", "concurrent", "error"],
        default="basic",
        help="Type of tests to run (default: basic)"
    )
    
    parser.add_argument(
        "--check-deps",
        action="store_true",
        help="Check dependencies and exit"
    )
    
    parser.add_argument(
        "--verbose", "-v",
        action="store_true",
        help="Verbose output"
    )
    
    parser.add_argument(
        "--markers", "-m",
        help="Additional pytest markers"
    )
    
    args = parser.parse_args()
    
    if args.check_deps:
        success = check_dependencies()
        sys.exit(0 if success else 1)
    
    print("RTMP Video Processing API - Integration Tests")
    print("=" * 50)
    
    # 检查依赖
    if not check_dependencies():
        print("\nPlease install missing dependencies before running tests.")
        sys.exit(1)
    
    print(f"\nRunning {args.type} integration tests...")
    
    # 运行测试
    success = run_tests(
        test_type=args.type,
        verbose=args.verbose,
        markers=args.markers
    )
    
    if success:
        print("\n✓ All tests passed!")
        sys.exit(0)
    else:
        print("\n✗ Some tests failed!")
        sys.exit(1)


if __name__ == "__main__":
    main()