"""
结构化日志和错误处理系统
配置结构化日志输出，包含会话ID、操作类型、时间戳
实现分层错误处理，覆盖API、RTMP、SSE、视频处理各层
"""
import logging
import logging.handlers
import json
import sys
import traceback
from datetime import datetime
from typing import Dict, Any, Optional, Union
from pathlib import Path
from contextlib import contextmanager
from functools import wraps
import asyncio

from config import get_config


class StructuredFormatter(logging.Formatter):
    """结构化日志格式化器，输出JSON格式的日志"""
    
    def format(self, record: logging.LogRecord) -> str:
        """格式化日志记录为JSON格式"""
        # 基础日志信息
        log_data = {
            "timestamp": datetime.fromtimestamp(record.created).isoformat(),
            "level": record.levelname,
            "logger": record.name,
            "message": record.getMessage(),
            "module": record.module,
            "function": record.funcName,
            "line": record.lineno
        }
        
        # 添加会话ID（如果存在）
        if hasattr(record, 'session_key'):
            log_data["session_key"] = record.session_key
        
        # 添加操作类型（如果存在）
        if hasattr(record, 'operation'):
            log_data["operation"] = record.operation
        
        # 添加用户ID（如果存在）
        if hasattr(record, 'user_id'):
            log_data["user_id"] = record.user_id
        
        # 添加请求ID（如果存在）
        if hasattr(record, 'request_id'):
            log_data["request_id"] = record.request_id
        
        # 添加额外的上下文数据
        if hasattr(record, 'extra_data'):
            log_data["extra"] = record.extra_data
        
        # 添加异常信息
        if record.exc_info:
            log_data["exception"] = {
                "type": record.exc_info[0].__name__,
                "message": str(record.exc_info[1]),
                "traceback": traceback.format_exception(*record.exc_info)
            }
        
        # 添加堆栈信息（如果存在）
        if hasattr(record, 'stack_info') and record.stack_info:
            log_data["stack_info"] = record.stack_info
        
        # 确保JSON序列化不会出错
        try:
            return json.dumps(log_data, ensure_ascii=False, default=str)
        except Exception as e:
            # 如果JSON序列化失败，返回简化版本
            fallback_data = {
                "timestamp": log_data.get("timestamp", ""),
                "level": log_data.get("level", ""),
                "logger": log_data.get("logger", ""),
                "message": f"Failed to serialize log data: {str(e)} - Original message: {log_data.get('message', '')}"
            }
            return json.dumps(fallback_data, ensure_ascii=False)


class SessionLoggerAdapter(logging.LoggerAdapter):
    """会话日志适配器，自动添加会话相关信息"""
    
    def __init__(self, logger: logging.Logger, session_key: str, operation: str = None):
        self.session_key = session_key
        self.operation = operation
        super().__init__(logger, {})
    
    def process(self, msg: str, kwargs: Dict[str, Any]) -> tuple:
        """处理日志消息，添加会话信息"""
        extra = kwargs.get('extra', {})
        extra['session_key'] = self.session_key
        if self.operation:
            extra['operation'] = self.operation
        kwargs['extra'] = extra
        return msg, kwargs
    
    def set_operation(self, operation: str) -> None:
        """设置当前操作类型"""
        self.operation = operation


class ErrorHandler:
    """错误处理器基类"""
    
    def __init__(self, logger: logging.Logger):
        self.logger = logger
    
    def handle_error(self, error: Exception, context: Dict[str, Any] = None) -> None:
        """处理错误"""
        context = context or {}
        self.logger.error(
            f"Error occurred: {str(error)}",
            exc_info=True,
            extra={'extra_data': context}
        )
    
    def handle_warning(self, message: str, context: Dict[str, Any] = None) -> None:
        """处理警告"""
        context = context or {}
        self.logger.warning(
            message,
            extra={'extra_data': context}
        )


class APIErrorHandler(ErrorHandler):
    """API层错误处理器"""
    
    def handle_validation_error(self, error: Exception, request_data: Dict[str, Any] = None) -> Dict[str, Any]:
        """处理API参数验证错误"""
        context = {
            "error_type": "validation_error",
            "request_data": request_data
        }
        self.handle_error(error, context)
        
        return {
            "error": "Invalid request parameters",
            "detail": str(error),
            "type": "validation_error"
        }
    
    def handle_session_not_found(self, session_key: str) -> Dict[str, Any]:
        """处理会话不存在错误"""
        context = {
            "error_type": "session_not_found",
            "session_key": session_key
        }
        self.handle_warning(f"Session not found: {session_key}", context)
        
        return {
            "error": "Session not found",
            "detail": f"Session {session_key} does not exist",
            "type": "session_not_found"
        }
    
    def handle_internal_error(self, error: Exception, operation: str = "Unknown") -> Dict[str, Any]:
        """处理内部服务器错误"""
        context = {
            "error_type": "internal_error",
            "operation": operation
        }
        self.handle_error(error, context)
        
        return {
            "error": "Internal server error",
            "detail": "An unexpected error occurred",
            "type": "internal_error"
        }


class RTMPErrorHandler(ErrorHandler):
    """RTMP层错误处理器"""
    
    def handle_connection_error(self, stream_key: str, error: Exception) -> None:
        """处理RTMP连接错误"""
        context = {
            "error_type": "rtmp_connection_error",
            "stream_key": stream_key
        }
        self.handle_error(error, context)
    
    def handle_stream_validation_error(self, stream_key: str, reason: str) -> None:
        """处理流验证错误"""
        context = {
            "error_type": "stream_validation_error",
            "stream_key": stream_key,
            "reason": reason
        }
        self.handle_warning(f"Stream validation failed for {stream_key}: {reason}", context)
    
    def handle_decoding_error(self, stream_key: str, error: Exception) -> None:
        """处理视频解码错误"""
        context = {
            "error_type": "video_decoding_error",
            "stream_key": stream_key
        }
        self.handle_error(error, context)


class SSEErrorHandler(ErrorHandler):
    """SSE层错误处理器"""
    
    def handle_connection_error(self, session_key: str, error: Exception) -> None:
        """处理SSE连接错误"""
        context = {
            "error_type": "sse_connection_error",
            "session_key": session_key
        }
        self.handle_error(error, context)
    
    def handle_client_disconnect(self, session_key: str) -> None:
        """处理客户端断开连接"""
        context = {
            "error_type": "sse_client_disconnect",
            "session_key": session_key
        }
        self.logger.info(f"SSE client disconnected: {session_key}", extra={'extra_data': context})
    
    def handle_queue_error(self, session_key: str, error: Exception) -> None:
        """处理队列操作错误"""
        context = {
            "error_type": "sse_queue_error",
            "session_key": session_key
        }
        self.handle_error(error, context)


class VideoProcessingErrorHandler(ErrorHandler):
    """视频处理层错误处理器"""
    
    def handle_processor_initialization_error(self, processor_type: str, error: Exception) -> None:
        """处理处理器初始化错误"""
        context = {
            "error_type": "processor_initialization_error",
            "processor_type": processor_type
        }
        self.handle_error(error, context)
    
    def handle_frame_processing_error(self, session_key: str, error: Exception, frame_info: Dict[str, Any] = None) -> None:
        """处理帧处理错误"""
        context = {
            "error_type": "frame_processing_error",
            "session_key": session_key,
            "frame_info": frame_info
        }
        self.handle_error(error, context)
    
    def handle_processor_cleanup_error(self, session_key: str, processor_type: str, error: Exception) -> None:
        """处理处理器清理错误"""
        context = {
            "error_type": "processor_cleanup_error",
            "session_key": session_key,
            "processor_type": processor_type
        }
        self.handle_error(error, context)



class QueueErrorHandler(ErrorHandler):
    """队列层错误处理器"""
    
    def handle_queue_full_error(self, queue_name: str, error: Exception) -> None:
        """处理队列满错误"""
        context = {
            "error_type": "queue_full_error",
            "queue_name": queue_name
        }
        self.handle_error(error, context)
    
    def handle_queue_operation_error(self, queue_name: str, operation: str, error: Exception) -> None:
        """处理队列操作错误"""
        context = {
            "error_type": "queue_operation_error",
            "queue_name": queue_name,
            "operation": operation
        }
        self.handle_error(error, context)


class ConfigErrorHandler(ErrorHandler):
    """配置层错误处理器"""
    
    def handle_config_load_error(self, config_path: str, error: Exception) -> None:
        """处理配置加载错误"""
        context = {
            "error_type": "config_load_error",
            "config_path": config_path
        }
        self.handle_error(error, context)
    
    def handle_config_validation_error(self, section: str, key: str, error: Exception) -> None:
        """处理配置验证错误"""
        context = {
            "error_type": "config_validation_error",
            "section": section,
            "key": key
        }
        self.handle_error(error, context)


class LoggingManager:
    """日志管理器"""
    
    def __init__(self):
        self._initialized = False
        self._loggers: Dict[str, logging.Logger] = {}
        self._error_handlers: Dict[str, ErrorHandler] = {}
    
    def initialize(self, config=None) -> None:
        """初始化日志系统"""
        if self._initialized:
            return
        
        if config is None:
            try:
                config = get_config()
            except RuntimeError:
                # 如果配置未初始化，使用默认配置
                from config import LoggingConfig
                config = type('Config', (), {'logging': LoggingConfig()})()
        
        # 配置根日志记录器
        root_logger = logging.getLogger()
        root_logger.setLevel(getattr(logging, config.logging.level))
        
        # 清除现有处理器
        for handler in root_logger.handlers[:]:
            root_logger.removeHandler(handler)
        
        # 创建结构化格式化器
        structured_formatter = StructuredFormatter()
        
        # 控制台处理器
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setFormatter(structured_formatter)
        console_handler.setLevel(getattr(logging, config.logging.level))
        root_logger.addHandler(console_handler)
        
        # 文件处理器（如果配置了文件路径）
        if config.logging.file_path:
            try:
                # 确保日志目录存在
                log_dir = Path(config.logging.file_path).parent
                log_dir.mkdir(parents=True, exist_ok=True)
                
                # 使用RotatingFileHandler支持日志轮转
                file_handler = logging.handlers.RotatingFileHandler(
                    config.logging.file_path,
                    maxBytes=config.logging.max_file_size,
                    backupCount=config.logging.backup_count,
                    encoding='utf-8'
                )
                file_handler.setFormatter(structured_formatter)
                file_handler.setLevel(getattr(logging, config.logging.level))
                root_logger.addHandler(file_handler)
                
                root_logger.info(f"File logging enabled: {config.logging.file_path}")
                
            except Exception as e:
                root_logger.error(f"Failed to setup file logging: {e}")
        
        # 初始化错误处理器
        self._initialize_error_handlers()
        
        self._initialized = True
        root_logger.info("Logging system initialized with structured format")
    
    def _initialize_error_handlers(self) -> None:
        """初始化各层错误处理器"""
        base_logger = logging.getLogger("error_handler")
        
        self._error_handlers = {
            "api": APIErrorHandler(logging.getLogger("api.error")),
            "rtmp": RTMPErrorHandler(logging.getLogger("rtmp.error")),
            "sse": SSEErrorHandler(logging.getLogger("sse.error")),
            "video_processing": VideoProcessingErrorHandler(logging.getLogger("video_processing.error")),
            "queue": QueueErrorHandler(logging.getLogger("queue.error")),
            "config": ConfigErrorHandler(logging.getLogger("config.error"))
        }
    def get_logger(self, name: str) -> logging.Logger:
        """获取指定名称的日志记录器"""
        if not self._initialized:
            self.initialize()
        
        if name not in self._loggers:
            self._loggers[name] = logging.getLogger(name)
        
        return self._loggers[name]
    
    def get_session_logger(self, session_key: str, operation: str = None, logger_name: str = "session") -> SessionLoggerAdapter:
        """获取会话日志适配器"""
        base_logger = self.get_logger(logger_name)
        return SessionLoggerAdapter(base_logger, session_key, operation)
    
    def get_error_handler(self, layer: str) -> ErrorHandler:
        """获取指定层的错误处理器"""
        if not self._initialized:
            self.initialize()
        
        return self._error_handlers.get(layer)


# 全局日志管理器实例
logging_manager = LoggingManager()


def get_logger(name: str) -> logging.Logger:
    """获取日志记录器的便捷函数"""
    return logging_manager.get_logger(name)


def get_session_logger(session_key: str, operation: str = None, logger_name: str = "session") -> SessionLoggerAdapter:
    """获取会话日志适配器的便捷函数"""
    return logging_manager.get_session_logger(session_key, operation, logger_name)


def get_error_handler(layer: str) -> ErrorHandler:
    """获取错误处理器的便捷函数"""
    return logging_manager.get_error_handler(layer)


# 装饰器用于自动错误处理和日志记录
def log_errors(layer: str = "general", operation: str = None):
    """错误日志装饰器"""
    def decorator(func):
        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            error_handler = get_error_handler(layer)
            logger = get_logger(f"{layer}.{func.__name__}")
            
            try:
                if operation:
                    logger.debug(f"Starting operation: {operation}")
                
                result = func(*args, **kwargs)
                
                if operation:
                    logger.debug(f"Completed operation: {operation}")
                
                return result
                
            except Exception as e:
                context = {
                    "function": func.__name__,
                    "operation": operation,
                    "args": str(args)[:200],  # 限制长度避免日志过长
                    "kwargs": str(kwargs)[:200]
                }
                
                if error_handler:
                    error_handler.handle_error(e, context)
                else:
                    logger.error(f"Error in {func.__name__}: {e}", exc_info=True, extra={'extra_data': context})
                
                raise
        
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            error_handler = get_error_handler(layer)
            logger = get_logger(f"{layer}.{func.__name__}")
            
            try:
                if operation:
                    logger.debug(f"Starting async operation: {operation}")
                
                result = await func(*args, **kwargs)
                
                if operation:
                    logger.debug(f"Completed async operation: {operation}")
                
                return result
                
            except Exception as e:
                context = {
                    "function": func.__name__,
                    "operation": operation,
                    "args": str(args)[:200],
                    "kwargs": str(kwargs)[:200]
                }
                
                if error_handler:
                    error_handler.handle_error(e, context)
                else:
                    logger.error(f"Error in {func.__name__}: {e}", exc_info=True, extra={'extra_data': context})
                
                raise
        
        return async_wrapper if asyncio.iscoroutinefunction(func) else sync_wrapper
    
    return decorator


@contextmanager
def log_operation(session_key: str = None, operation: str = None, logger_name: str = "operation"):
    """操作日志上下文管理器"""
    if session_key:
        logger = get_session_logger(session_key, operation, logger_name)
    else:
        logger = get_logger(logger_name)
    
    start_time = datetime.now()
    
    try:
        if operation:
            logger.info(f"Starting operation: {operation}")
        
        yield logger
        
        if operation:
            duration = (datetime.now() - start_time).total_seconds()
            logger.info(f"Completed operation: {operation} (duration: {duration:.3f}s)")
    
    except asyncio.CancelledError:
        if operation:
            duration = (datetime.now() - start_time).total_seconds()
            logger.info(f"Cancelled operation: {operation} (duration: {duration:.3f}s)")
        raise
    except Exception as e:
        if operation:
            duration = (datetime.now() - start_time).total_seconds()
            logger.error(f"Failed operation: {operation} (duration: {duration:.3f}s): {e}", exc_info=True)
        raise


def setup_logging(config=None) -> None:
    """设置日志系统的便捷函数"""
    logging_manager.initialize(config)


# 在模块导入时自动初始化日志系统
try:
    setup_logging()
except Exception:
    # 如果初始化失败，使用基本的日志配置
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )