"""
配置管理系统
支持从TOML配置文件读取设置，使用pydantic进行配置验证和类型转换
"""
import os
import logging
from pathlib import Path
from typing import Optional, Dict, Any

try:
    import tomllib  # Python 3.11+
except ImportError:
    import tomli as tomllib  # fallback for older Python versions

from pydantic import BaseModel, Field, field_validator, ConfigDict


logger = logging.getLogger(__name__)


class RTMPConfig(BaseModel):
    """RTMP服务器配置"""
    host: str = Field(default="0.0.0.0", description="RTMP服务器监听地址")
    port: int = Field(default=1935, ge=1, le=65535, description="RTMP服务器端口")
    
    @field_validator('host')
    @classmethod
    def validate_host(cls, v):
        if not v or not isinstance(v, str):
            raise ValueError("RTMP host must be a non-empty string")
        return v


class FastAPIConfig(BaseModel):
    """FastAPI服务器配置"""
    host: str = Field(default="0.0.0.0", description="API服务器监听地址")
    port: int = Field(default=8000, ge=1, le=65535, description="API服务器端口")
    reload: bool = Field(default=False, description="是否启用自动重载")
    log_level: str = Field(default="info", description="日志级别")
    cors_origins: list = Field(default=["*"], description="CORS允许的源")
    
    @field_validator('log_level')
    @classmethod
    def validate_log_level(cls, v):
        valid_levels = ['debug', 'info', 'warning', 'error', 'critical']
        if v.lower() not in valid_levels:
            raise ValueError(f"Log level must be one of: {valid_levels}")
        return v.lower()


class SessionConfig(BaseModel):
    """会话管理配置"""
    timeout: int = Field(default=3600, ge=60, description="会话超时时间（秒）")
    max_concurrent_sessions: int = Field(default=100, ge=1, description="最大并发会话数")
    cleanup_interval: int = Field(default=300, ge=60, description="会话清理间隔（秒）")
    
    @field_validator('timeout')
    @classmethod
    def validate_timeout(cls, v):
        if v < 60:
            raise ValueError("Session timeout must be at least 60 seconds")
        return v


class QueueConfig(BaseModel):
    """队列配置"""
    max_size: int = Field(default=1000, ge=10, description="队列最大大小")
    timeout: float = Field(default=1.0, ge=0.1, description="队列操作超时时间（秒）")
    frame_queue_size: int = Field(default=100, ge=10, description="帧队列大小")
    
    @field_validator('max_size', 'frame_queue_size')
    @classmethod
    def validate_queue_size(cls, v):
        if v < 10:
            raise ValueError("Queue size must be at least 10")
        return v


class ProcessingConfig(BaseModel):
    """视频处理配置"""
    max_frame_rate: int = Field(default=30, ge=1, le=60, description="最大处理帧率")
    frame_skip_threshold: int = Field(default=5, ge=1, description="跳帧阈值")
    processing_timeout: float = Field(default=5.0, ge=0.1, description="单帧处理超时时间（秒）")
    
    @field_validator('max_frame_rate')
    @classmethod
    def validate_frame_rate(cls, v):
        if not 1 <= v <= 60:
            raise ValueError("Frame rate must be between 1 and 60")
        return v


class LoggingConfig(BaseModel):
    """日志配置"""
    level: str = Field(default="INFO", description="日志级别")
    format: str = Field(
        default="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        description="日志格式"
    )
    file_path: Optional[str] = Field(default=None, description="日志文件路径")
    max_file_size: int = Field(default=10485760, ge=1048576, description="日志文件最大大小（字节）")
    backup_count: int = Field(default=5, ge=1, description="日志文件备份数量")
    
    @field_validator('level')
    @classmethod
    def validate_level(cls, v):
        valid_levels = ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']
        v_upper = v.upper()
        if v_upper not in valid_levels:
            raise ValueError(f"Log level must be one of: {valid_levels}")
        return v_upper


class Config(BaseModel):
    """主配置类"""
    rtmp: RTMPConfig = Field(default_factory=RTMPConfig)
    fastapi: FastAPIConfig = Field(default_factory=FastAPIConfig)
    session: SessionConfig = Field(default_factory=SessionConfig)
    queue: QueueConfig = Field(default_factory=QueueConfig)
    processing: ProcessingConfig = Field(default_factory=ProcessingConfig)
    logging: LoggingConfig = Field(default_factory=LoggingConfig)
    
    # 使用ConfigDict替代class Config
    model_config = ConfigDict(
        # 允许额外字段，便于扩展
        extra="allow",
        # 使用枚举值
        use_enum_values=True
    )
    
    @classmethod
    def from_toml(cls, config_path: str = "config.toml") -> "Config":
        """
        从TOML文件加载配置
        
        Args:
            config_path: 配置文件路径
            
        Returns:
            Config实例
            
        Raises:
            FileNotFoundError: 配置文件不存在
            ValueError: 配置文件格式错误或验证失败
        """
        config_file = Path(config_path)
        
        if not config_file.exists():
            logger.warning(f"Configuration file {config_path} not found, using default settings")
            return cls()
        
        try:
            with open(config_file, 'rb') as f:
                config_data = tomllib.load(f)
            
            logger.info(f"Loaded configuration from {config_path}")
            return cls(**config_data)
            
        except tomllib.TOMLDecodeError as e:
            raise ValueError(f"Invalid TOML format in {config_path}: {e}")
        except Exception as e:
            raise ValueError(f"Error loading configuration from {config_path}: {e}")
    
    @classmethod
    def from_env(cls) -> "Config":
        """
        从环境变量加载配置
        环境变量格式: APP_SECTION_KEY (例如: APP_RTMP_HOST)
        
        Returns:
            Config实例
        """
        config_data = {}
        
        # 定义环境变量映射
        env_mappings = {
            'APP_RTMP_HOST': ('rtmp', 'host'),
            'APP_RTMP_PORT': ('rtmp', 'port'),
            'APP_FASTAPI_HOST': ('fastapi', 'host'),
            'APP_FASTAPI_PORT': ('fastapi', 'port'),
            'APP_FASTAPI_RELOAD': ('fastapi', 'reload'),
            'APP_FASTAPI_LOG_LEVEL': ('fastapi', 'log_level'),
            'APP_SESSION_TIMEOUT': ('session', 'timeout'),
            'APP_SESSION_MAX_CONCURRENT': ('session', 'max_concurrent_sessions'),
            'APP_QUEUE_MAX_SIZE': ('queue', 'max_size'),
            'APP_QUEUE_TIMEOUT': ('queue', 'timeout'),
            'APP_PROCESSING_MAX_FRAME_RATE': ('processing', 'max_frame_rate'),
            'APP_LOGGING_LEVEL': ('logging', 'level'),
            'APP_LOGGING_FILE_PATH': ('logging', 'file_path'),
        }
        
        for env_key, (section, key) in env_mappings.items():
            value = os.getenv(env_key)
            if value is not None:
                if section not in config_data:
                    config_data[section] = {}
                
                # 类型转换
                if key in ['port', 'timeout', 'max_concurrent_sessions', 'max_size', 'max_frame_rate']:
                    try:
                        config_data[section][key] = int(value)
                    except ValueError:
                        logger.warning(f"Invalid integer value for {env_key}: {value}")
                        continue
                elif key in ['reload']:
                    config_data[section][key] = value.lower() in ('true', '1', 'yes', 'on')
                elif key in ['timeout'] and section == 'queue':
                    try:
                        config_data[section][key] = float(value)
                    except ValueError:
                        logger.warning(f"Invalid float value for {env_key}: {value}")
                        continue
                else:
                    config_data[section][key] = value
        
        if config_data:
            logger.info("Loaded configuration from environment variables")
        
        return cls(**config_data)
    
    def to_dict(self) -> Dict[str, Any]:
        """
        将配置转换为字典格式
        
        Returns:
            配置字典
        """
        return self.model_dump()
    
    def save_to_toml(self, config_path: str = "config.toml") -> None:
        """
        将当前配置保存到TOML文件
        
        Args:
            config_path: 配置文件路径
        """
        try:
            import tomli_w
        except ImportError:
            raise ImportError("tomli_w is required for saving TOML files. Install with: pip install tomli_w")
        
        config_data = self.to_dict()
        
        with open(config_path, 'wb') as f:
            tomli_w.dump(config_data, f)
        
        logger.info(f"Configuration saved to {config_path}")
    
    def setup_logging(self) -> None:
        """
        根据配置设置日志系统
        """
        import logging.handlers
        from logging_config import StructuredFormatter
        
        # 创建根日志记录器
        root_logger = logging.getLogger()
        root_logger.setLevel(getattr(logging, self.logging.level))
        
        # 清除现有处理器
        for handler in root_logger.handlers[:]:
            root_logger.removeHandler(handler)
        
        # 创建结构化格式化器
        formatter = StructuredFormatter()
        
        # 控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        root_logger.addHandler(console_handler)
        
        # 文件处理器（如果配置了文件路径）
        if self.logging.file_path:
            try:
                # 确保日志目录存在
                log_dir = Path(self.logging.file_path).parent
                log_dir.mkdir(parents=True, exist_ok=True)
                
                # 使用RotatingFileHandler支持日志轮转
                file_handler = logging.handlers.RotatingFileHandler(
                    self.logging.file_path,
                    maxBytes=self.logging.max_file_size,
                    backupCount=self.logging.backup_count,
                    encoding='utf-8'
                )
                file_handler.setFormatter(formatter)
                root_logger.addHandler(file_handler)
                
                # 使用结构化日志记录器记录信息
                logger.info("File logging enabled", extra={'extra_data': {'file_path': self.logging.file_path}})
                
            except Exception as e:
                logger.error("Failed to setup file logging", exc_info=True, extra={'extra_data': {'error': str(e)}})


# 全局配置实例
config: Optional[Config] = None


def load_config(config_path: str = "config.toml", use_env: bool = True) -> Config:
    """
    加载配置
    
    Args:
        config_path: TOML配置文件路径
        use_env: 是否使用环境变量覆盖配置
        
    Returns:
        Config实例
    """
    global config
    
    # 首先从TOML文件加载
    config = Config.from_toml(config_path)
    
    # 如果启用环境变量，则用环境变量覆盖
    if use_env:
        env_config = Config.from_env()
        # 合并配置（环境变量优先）
        config_dict = config.to_dict()
        env_dict = env_config.to_dict()
        
        # 深度合并配置
        for section, values in env_dict.items():
            if section in config_dict and isinstance(values, dict):
                config_dict[section].update(values)
            else:
                config_dict[section] = values
        
        config = Config(**config_dict)
    
    # 设置日志系统
    config.setup_logging()
    
    logger.info("Configuration loaded successfully")
    return config


def get_config() -> Config:
    """
    获取全局配置实例
    
    Returns:
        Config实例
        
    Raises:
        RuntimeError: 配置未初始化
    """
    global config
    if config is None:
        raise RuntimeError("Configuration not initialized. Call load_config() first.")
    return config


def reload_config(config_path: str = "config.toml", use_env: bool = True) -> Config:
    """
    重新加载配置
    
    Args:
        config_path: TOML配置文件路径
        use_env: 是否使用环境变量覆盖配置
        
    Returns:
        Config实例
    """
    logger.info("Reloading configuration...")
    return load_config(config_path, use_env)