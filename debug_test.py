#!/usr/bin/env python3
"""
Debug test to isolate the video processing issue
"""
import asyncio
import pytest
import pytest_asyncio
import httpx
import subprocess
import time
import threading
import json
import os
from typing import List, Dict, Any, Optional

from main import ApplicationManager
from server.session_manager import SessionManager, ProcessorFactory
from processors.processors import DummyProcessor
from config import load_config


class SimpleSSEClient:
    """简化的SSE客户端用于调试"""
    
    def __init__(self, url: str):
        self.url = url
        self.events: List[Dict[str, Any]] = []
        self.is_running = False
        self.thread: Optional[threading.Thread] = None
        self.error: Optional[Exception] = None
        
    def start(self):
        """开始接收SSE事件"""
        self.is_running = True
        self.thread = threading.Thread(target=self._listen_events, daemon=True)
        self.thread.start()
        
    def stop(self):
        """停止接收SSE事件"""
        self.is_running = False
        if self.thread:
            self.thread.join(timeout=5)
            
    def _listen_events(self):
        """监听SSE事件的线程函数"""
        try:
            with httpx.stream('GET', self.url, timeout=30.0) as response:
                if response.status_code != 200:
                    self.error = Exception(f"SSE connection failed: {response.status_code}")
                    return
                
                current_event_type = None
                
                for line in response.iter_lines():
                    if not self.is_running:
                        break
                        
                    line = line.strip()
                    if not line:  # 空行表示事件结束
                        continue
                        
                    print(f"SSE Line: {line}")  # Debug output
                        
                    if line.startswith('event:'):
                        current_event_type = line[6:].strip()
                        print(f"Event type: {current_event_type}")
                    elif line.startswith('data:'):
                        data = line[5:].strip()
                        print(f"Event data: {data}")
                        try:
                            # 尝试解析JSON数据
                            if data.startswith('{') and data.endswith('}'):
                                data = json.loads(data)
                            
                            self.events.append({
                                'type': current_event_type if current_event_type else 'unknown',
                                'data': data,
                                'timestamp': time.time()
                            })
                            
                            print(f"Added event: {current_event_type}, total events: {len(self.events)}")
                            
                            # 重置事件类型
                            current_event_type = None
                            
                        except json.JSONDecodeError as e:
                            # 如果不是JSON，直接存储字符串
                            self.events.append({
                                'type': current_event_type if current_event_type else 'unknown',
                                'data': data,
                                'timestamp': time.time()
                            })
                            current_event_type = None
                            
        except Exception as e:
            print(f"SSE Error: {e}")
            self.error = e
    
    def wait_for_events(self, count: int, timeout: float = 10.0) -> bool:
        """等待指定数量的事件"""
        start_time = time.time()
        while len(self.events) < count and time.time() - start_time < timeout:
            if self.error:
                raise self.error
            time.sleep(0.1)
        return len(self.events) >= count
    
    def get_events_by_type(self, event_type: str) -> List[Dict[str, Any]]:
        """获取指定类型的事件"""
        return [event for event in self.events if event.get('type') == event_type]


class SimpleRTMPStreamer:
    """简化的RTMP推流器"""
    
    def __init__(self, rtmp_url: str, duration: int = 5):
        self.rtmp_url = rtmp_url
        self.duration = duration
        self.process: Optional[subprocess.Popen] = None
        self.is_running = False
        
    def start_streaming(self) -> bool:
        """开始RTMP推流"""
        try:
            # 检查FFmpeg是否可用
            subprocess.run(['ffmpeg', '-version'], capture_output=True, check=True)
        except (subprocess.CalledProcessError, FileNotFoundError):
            print("FFmpeg not available")
            return False
        
        # 简化的FFmpeg命令
        cmd = [
            'ffmpeg',
            '-f', 'lavfi',
            '-i', f'testsrc=duration={self.duration}:size=320x240:rate=1',
            '-c:v', 'libx264',
            '-preset', 'ultrafast',
            '-tune', 'zerolatency',
            '-f', 'flv',
            '-y',
            self.rtmp_url
        ]
        
        print(f"Starting FFmpeg with command: {' '.join(cmd)}")
        
        try:
            self.process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                stdin=subprocess.PIPE
            )
            self.is_running = True
            print("FFmpeg process started")
            return True
        except Exception as e:
            print(f"Failed to start FFmpeg streaming: {e}")
            return False
    
    def stop_streaming(self):
        """停止RTMP推流"""
        if self.process and self.is_running:
            try:
                print("Stopping FFmpeg...")
                self.process.terminate()
                try:
                    self.process.wait(timeout=5)
                except subprocess.TimeoutExpired:
                    self.process.kill()
                    self.process.wait()
            except Exception as e:
                print(f"Error stopping FFmpeg: {e}")
            finally:
                self.is_running = False
                self.process = None
                print("FFmpeg stopped")
    
    def __enter__(self):
        self.start_streaming()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.stop_streaming()


@pytest_asyncio.fixture(scope="session")
async def debug_app_manager():
    """调试用应用管理器fixture"""
    # 注册测试处理器
    ProcessorFactory.register_processor("test", DummyProcessor)
    
    config = load_config("tests/test_config.toml", use_env=False)
    app = ApplicationManager()
    app.config = config
    
    try:
        # 初始化日志系统
        app.init_logging()
        
        # 创建组件
        app.create_components()
        
        # 启动服务
        await app.start_services()
        
        # 等待服务启动
        await asyncio.sleep(2)
        
        print("Debug app manager ready")
        yield app
        
    finally:
        # 清理
        try:
            await app.shutdown_services()
        except Exception as e:
            print(f"Error during cleanup: {e}")


@pytest.mark.asyncio
async def test_debug_video_processing(debug_app_manager):
    """调试视频处理流程"""
    config = debug_app_manager.config
    
    # 1. 创建会话
    base_url = f"http://{config.fastapi.host}:{config.fastapi.port}"
    async with httpx.AsyncClient(base_url=base_url, timeout=30.0) as client:
        print("Creating session...")
        response = await client.post("/api/session", json={"analysis_type": "test"})
        assert response.status_code == 200
        
        session_data = response.json()
        session_key = session_data["session_key"]
        print(f"Created session: {session_key}")
        
        # 2. 建立SSE连接
        sse_url = f"{base_url}/events/{session_key}"
        sse_client = SimpleSSEClient(sse_url)
        sse_client.start()
        
        print("SSE client started, waiting for connection...")
        await asyncio.sleep(2)
        
        try:
            # 3. 开始RTMP推流
            rtmp_url = f"rtmp://{config.rtmp.host}:{config.rtmp.port}/live/{session_key}"
            print(f"Starting RTMP stream to: {rtmp_url}")
            
            with SimpleRTMPStreamer(rtmp_url, duration=10) as streamer:
                if not streamer.is_running:
                    pytest.skip("FFmpeg streaming failed to start")
                
                print("Streaming started, waiting for events...")
                
                # 等待更长时间以便观察
                for i in range(15):  # 15秒
                    await asyncio.sleep(1)
                    print(f"Waiting... {i+1}/15, events so far: {len(sse_client.events)}")
                    
                    # 打印当前事件
                    if sse_client.events:
                        for j, event in enumerate(sse_client.events):
                            print(f"  Event {j}: {event.get('type')} - {event.get('data')}")
                
                print(f"Final event count: {len(sse_client.events)}")
                
                # 验证结果
                connected_events = sse_client.get_events_by_type('connected')
                result_events = sse_client.get_events_by_type('result')
                heartbeat_events = sse_client.get_events_by_type('heartbeat')
                
                print(f"Connected events: {len(connected_events)}")
                print(f"Result events: {len(result_events)}")
                print(f"Heartbeat events: {len(heartbeat_events)}")
                
                # 至少应该有连接事件
                assert len(connected_events) >= 1, "Should receive connected event"
                
                # 如果有结果事件，验证内容
                if result_events:
                    for event in result_events:
                        data = event['data']
                        if isinstance(data, dict):
                            print(f"Result event data: {data}")
                            assert 'analysis_data' in data
                            assert 'processor_type' in data['analysis_data']
                            assert data['analysis_data']['processor_type'] == 'dummy'
                
        finally:
            sse_client.stop()
            
            # 清理会话
            await client.delete(f"/api/session/{session_key}")
            print("Session cleaned up")


if __name__ == "__main__":
    # 运行调试测试
    pytest.main([__file__, "-v", "-s"])