"""
日志和错误处理系统测试
"""
import json
import logging
import pytest
import tempfile
import os
from pathlib import Path
from unittest.mock import patch, MagicMock

from ..logging_config import (
    StructuredFormatter, SessionLoggerAdapter, Error<PERSON><PERSON><PERSON>,
    APIErrorHandler, RTMPError<PERSON>andler, SSEErrorHandler, VideoProcessingErrorHandler,
    LoggingManager, get_logger, get_session_logger, get_error_handler,
    log_errors, log_operation, setup_logging
)


class TestStructuredFormatter:
    """结构化日志格式化器测试"""
    
    def test_basic_formatting(self):
        """测试基本格式化"""
        formatter = StructuredFormatter()
        record = logging.LogRecord(
            name="test_logger",
            level=logging.INFO,
            pathname="test_file.py",
            lineno=10,
            msg="Test message",
            args=(),
            exc_info=None
        )
        
        formatted = formatter.format(record)
        data = json.loads(formatted)
        
        assert data["level"] == "INFO"
        assert data["logger"] == "test_logger"
        assert data["message"] == "Test message"
        assert data["module"] == "test_file"
        assert data["line"] == 10
    
    def test_formatting_with_session_key(self):
        """测试带会话密钥的格式化"""
        formatter = StructuredFormatter()
        record = logging.LogRecord(
            name="test_logger",
            level=logging.INFO,
            pathname="test_file.py",
            lineno=10,
            msg="Test message",
            args=(),
            exc_info=None
        )
        record.session_key = "test-session-123"
        
        formatted = formatter.format(record)
        data = json.loads(formatted)
        
        assert data["session_key"] == "test-session-123"
    
    def test_formatting_with_operation(self):
        """测试带操作类型的格式化"""
        formatter = StructuredFormatter()
        record = logging.LogRecord(
            name="test_logger",
            level=logging.INFO,
            pathname="test_file.py",
            lineno=10,
            msg="Test message",
            args=(),
            exc_info=None
        )
        record.operation = "test_operation"
        
        formatted = formatter.format(record)
        data = json.loads(formatted)
        
        assert data["operation"] == "test_operation"
    
    def test_formatting_with_exception(self):
        """测试带异常信息的格式化"""
        formatter = StructuredFormatter()
        try:
            raise ValueError("Test error")
        except ValueError as e:
            record = logging.LogRecord(
                name="test_logger",
                level=logging.ERROR,
                pathname="test_file.py",
                lineno=10,
                msg="Error occurred",
                args=(),
                exc_info=(type(e), e, e.__traceback__)
            )
        
        formatted = formatter.format(record)
        data = json.loads(formatted)
        
        assert "exception" in data
        assert data["exception"]["type"] == "ValueError"
        assert data["exception"]["message"] == "Test error"
        assert isinstance(data["exception"]["traceback"], list)
    
    def test_formatting_with_extra_data(self):
        """测试带额外数据的格式化"""
        formatter = StructuredFormatter()
        record = logging.LogRecord(
            name="test_logger",
            level=logging.INFO,
            pathname="test_file.py",
            lineno=10,
            msg="Test message",
            args=(),
            exc_info=None
        )
        record.extra_data = {"key1": "value1", "key2": 123}
        
        formatted = formatter.format(record)
        data = json.loads(formatted)
        
        assert "extra" in data
        assert data["extra"]["key1"] == "value1"
        assert data["extra"]["key2"] == 123


class TestSessionLoggerAdapter:
    """会话日志适配器测试"""
    
    def test_basic_logging(self):
        """测试基本日志记录"""
        mock_logger = MagicMock(spec=logging.Logger)
        session_key = "test-session-456"
        adapter = SessionLoggerAdapter(mock_logger, session_key)
        
        adapter.info("Test message")
        
        mock_logger.info.assert_called_once()
        args, kwargs = mock_logger.info.call_args
        assert args[0] == "Test message"
        assert "extra" in kwargs
        assert kwargs["extra"]["session_key"] == session_key
    
    def test_logging_with_operation(self):
        """测试带操作类型的日志记录"""
        mock_logger = MagicMock(spec=logging.Logger)
        session_key = "test-session-456"
        operation = "test_operation"
        adapter = SessionLoggerAdapter(mock_logger, session_key, operation)
        
        adapter.info("Test message")
        
        mock_logger.info.assert_called_once()
        args, kwargs = mock_logger.info.call_args
        assert args[0] == "Test message"
        assert "extra" in kwargs
        assert kwargs["extra"]["session_key"] == session_key
        assert kwargs["extra"]["operation"] == operation
    
    def test_set_operation(self):
        """测试设置操作类型"""
        mock_logger = MagicMock(spec=logging.Logger)
        session_key = "test-session-456"
        adapter = SessionLoggerAdapter(mock_logger, session_key)
        
        adapter.set_operation("new_operation")
        adapter.info("Test message")
        
        mock_logger.info.assert_called_once()
        args, kwargs = mock_logger.info.call_args
        assert "extra" in kwargs
        assert kwargs["extra"]["operation"] == "new_operation"


class TestErrorHandler:
    """错误处理器测试"""
    
    def test_handle_error(self):
        """测试处理错误"""
        mock_logger = MagicMock(spec=logging.Logger)
        handler = ErrorHandler(mock_logger)
        error = ValueError("Test error")
        context = {"key": "value"}
        
        handler.handle_error(error, context)
        
        mock_logger.error.assert_called_once()
        args, kwargs = mock_logger.error.call_args
        assert "Test error" in args[0]
        assert kwargs["exc_info"] is True
        assert "extra_data" in kwargs["extra"]
        assert kwargs["extra"]["extra_data"] == context
    
    def test_handle_warning(self):
        """测试处理警告"""
        mock_logger = MagicMock(spec=logging.Logger)
        handler = ErrorHandler(mock_logger)
        message = "Warning message"
        context = {"key": "value"}
        
        handler.handle_warning(message, context)
        
        mock_logger.warning.assert_called_once()
        args, kwargs = mock_logger.warning.call_args
        assert args[0] == message
        assert "extra_data" in kwargs["extra"]
        assert kwargs["extra"]["extra_data"] == context


class TestAPIErrorHandler:
    """API错误处理器测试"""
    
    def test_handle_validation_error(self):
        """测试处理验证错误"""
        mock_logger = MagicMock(spec=logging.Logger)
        handler = APIErrorHandler(mock_logger)
        error = ValueError("Invalid parameter")
        request_data = {"param": "value"}
        
        result = handler.handle_validation_error(error, request_data)
        
        mock_logger.error.assert_called_once()
        assert "error" in result
        assert "detail" in result
        assert "type" in result
        assert result["type"] == "validation_error"
    
    def test_handle_session_not_found(self):
        """测试处理会话不存在错误"""
        mock_logger = MagicMock(spec=logging.Logger)
        handler = APIErrorHandler(mock_logger)
        session_key = "nonexistent-session"
        
        result = handler.handle_session_not_found(session_key)
        
        mock_logger.warning.assert_called_once()
        assert "error" in result
        assert "detail" in result
        assert "type" in result
        assert result["type"] == "session_not_found"
        assert session_key in result["detail"]
    
    def test_handle_internal_error(self):
        """测试处理内部错误"""
        mock_logger = MagicMock(spec=logging.Logger)
        handler = APIErrorHandler(mock_logger)
        error = Exception("Internal error")
        operation = "test_operation"
        
        result = handler.handle_internal_error(error, operation)
        
        mock_logger.error.assert_called_once()
        assert "error" in result
        assert "detail" in result
        assert "type" in result
        assert result["type"] == "internal_error"


class TestRTMPErrorHandler:
    """RTMP错误处理器测试"""
    
    def test_handle_connection_error(self):
        """测试处理连接错误"""
        mock_logger = MagicMock(spec=logging.Logger)
        handler = RTMPErrorHandler(mock_logger)
        stream_key = "test-stream"
        error = ConnectionError("Connection failed")
        
        handler.handle_connection_error(stream_key, error)
        
        mock_logger.error.assert_called_once()
        args, kwargs = mock_logger.error.call_args
        assert kwargs["exc_info"] is True
        assert "extra_data" in kwargs["extra"]
        assert kwargs["extra"]["extra_data"]["stream_key"] == stream_key
    
    def test_handle_stream_validation_error(self):
        """测试处理流验证错误"""
        mock_logger = MagicMock(spec=logging.Logger)
        handler = RTMPErrorHandler(mock_logger)
        stream_key = "test-stream"
        reason = "Invalid stream key"
        
        handler.handle_stream_validation_error(stream_key, reason)
        
        mock_logger.warning.assert_called_once()
        args, kwargs = mock_logger.warning.call_args
        assert stream_key in args[0]
        assert reason in args[0]
        assert "extra_data" in kwargs["extra"]
        assert kwargs["extra"]["extra_data"]["stream_key"] == stream_key
        assert kwargs["extra"]["extra_data"]["reason"] == reason


class TestSSEErrorHandler:
    """SSE错误处理器测试"""
    
    def test_handle_connection_error(self):
        """测试处理连接错误"""
        mock_logger = MagicMock(spec=logging.Logger)
        handler = SSEErrorHandler(mock_logger)
        session_key = "test-session"
        error = ConnectionError("Connection failed")
        
        handler.handle_connection_error(session_key, error)
        
        mock_logger.error.assert_called_once()
        args, kwargs = mock_logger.error.call_args
        assert kwargs["exc_info"] is True
        assert "extra_data" in kwargs["extra"]
        assert kwargs["extra"]["extra_data"]["session_key"] == session_key
    
    def test_handle_client_disconnect(self):
        """测试处理客户端断开连接"""
        mock_logger = MagicMock(spec=logging.Logger)
        handler = SSEErrorHandler(mock_logger)
        session_key = "test-session"
        
        handler.handle_client_disconnect(session_key)
        
        mock_logger.info.assert_called_once()
        args, kwargs = mock_logger.info.call_args
        assert session_key in args[0]
        assert "extra_data" in kwargs["extra"]
        assert kwargs["extra"]["extra_data"]["session_key"] == session_key


class TestVideoProcessingErrorHandler:
    """视频处理错误处理器测试"""
    
    def test_handle_processor_initialization_error(self):
        """测试处理处理器初始化错误"""
        mock_logger = MagicMock(spec=logging.Logger)
        handler = VideoProcessingErrorHandler(mock_logger)
        processor_type = "test_processor"
        error = RuntimeError("Initialization failed")
        
        handler.handle_processor_initialization_error(processor_type, error)
        
        mock_logger.error.assert_called_once()
        args, kwargs = mock_logger.error.call_args
        assert kwargs["exc_info"] is True
        assert "extra_data" in kwargs["extra"]
        assert kwargs["extra"]["extra_data"]["processor_type"] == processor_type
    
    def test_handle_frame_processing_error(self):
        """测试处理帧处理错误"""
        mock_logger = MagicMock(spec=logging.Logger)
        handler = VideoProcessingErrorHandler(mock_logger)
        session_key = "test-session"
        error = ValueError("Processing failed")
        frame_info = {"width": 640, "height": 480}
        
        handler.handle_frame_processing_error(session_key, error, frame_info)
        
        mock_logger.error.assert_called_once()
        args, kwargs = mock_logger.error.call_args
        assert kwargs["exc_info"] is True
        assert "extra_data" in kwargs["extra"]
        assert kwargs["extra"]["extra_data"]["session_key"] == session_key
        assert kwargs["extra"]["extra_data"]["frame_info"] == frame_info


class TestLoggingManager:
    """日志管理器测试"""
    
    def test_initialize(self):
        """测试初始化日志系统"""
        manager = LoggingManager()
        
        # 使用临时配置
        from ..config import Config, LoggingConfig
        config = Config()
        config.logging = LoggingConfig(level="DEBUG")
        
        manager.initialize(config)
        
        assert manager._initialized is True
        assert len(manager._loggers) == 0
        assert len(manager._error_handlers) == 4  # api, rtmp, sse, video_processing
    
    def test_get_logger(self):
        """测试获取日志记录器"""
        manager = LoggingManager()
        manager.initialize()
        
        logger = manager.get_logger("test_logger")
        
        assert isinstance(logger, logging.Logger)
        assert logger.name == "test_logger"
        assert "test_logger" in manager._loggers
    
    def test_get_session_logger(self):
        """测试获取会话日志适配器"""
        manager = LoggingManager()
        manager.initialize()
        
        session_logger = manager.get_session_logger("test-session", "test_operation")
        
        assert isinstance(session_logger, SessionLoggerAdapter)
        assert session_logger.session_key == "test-session"
        assert session_logger.operation == "test_operation"
    
    def test_get_error_handler(self):
        """测试获取错误处理器"""
        manager = LoggingManager()
        manager.initialize()
        
        api_handler = manager.get_error_handler("api")
        rtmp_handler = manager.get_error_handler("rtmp")
        sse_handler = manager.get_error_handler("sse")
        video_handler = manager.get_error_handler("video_processing")
        
        assert isinstance(api_handler, APIErrorHandler)
        assert isinstance(rtmp_handler, RTMPErrorHandler)
        assert isinstance(sse_handler, SSEErrorHandler)
        assert isinstance(video_handler, VideoProcessingErrorHandler)


class TestLogDecorators:
    """日志装饰器测试"""
    
    def test_log_errors_sync(self):
        """测试同步函数的错误日志装饰器"""
        mock_logger = MagicMock(spec=logging.Logger)
        mock_error_handler = MagicMock(spec=ErrorHandler)
        
        with patch("logging_config.get_logger", return_value=mock_logger), \
             patch("logging_config.get_error_handler", return_value=mock_error_handler):
            
            @log_errors("test_layer", "test_operation")
            def test_function(arg1, arg2=None):
                if arg2 is None:
                    raise ValueError("Test error")
                return arg1 + arg2
            
            # 测试正常执行
            result = test_function(1, 2)
            assert result == 3
            mock_logger.info.assert_any_call("Starting operation: test_operation")
            mock_logger.info.assert_any_call("Completed operation: test_operation")
            
            # 测试异常情况
            mock_logger.reset_mock()
            mock_error_handler.reset_mock()
            
            with pytest.raises(ValueError):
                test_function(1)
            
            mock_logger.info.assert_any_call("Starting operation: test_operation")
            mock_error_handler.handle_error.assert_called_once()
    
    def test_log_errors_async(self):
        """测试异步函数的错误日志装饰器"""
        mock_logger = MagicMock(spec=logging.Logger)
        mock_error_handler = MagicMock(spec=ErrorHandler)
        
        with patch("logging_config.get_logger", return_value=mock_logger), \
             patch("logging_config.get_error_handler", return_value=mock_error_handler):
            
            @log_errors("test_layer", "test_operation")
            async def test_async_function(arg1, arg2=None):
                if arg2 is None:
                    raise ValueError("Test error")
                return arg1 + arg2
            
            # 测试正常执行
            result = asyncio.run(test_async_function(1, 2))
            assert result == 3
            mock_logger.info.assert_any_call("Starting async operation: test_operation")
            mock_logger.info.assert_any_call("Completed async operation: test_operation")
            
            # 测试异常情况
            mock_logger.reset_mock()
            mock_error_handler.reset_mock()
            
            with pytest.raises(ValueError):
                asyncio.run(test_async_function(1))
            
            mock_logger.info.assert_any_call("Starting async operation: test_operation")
            mock_error_handler.handle_error.assert_called_once()
    
    def test_log_operation(self):
        """测试操作日志上下文管理器"""
        mock_logger = MagicMock(spec=logging.Logger)
        mock_session_logger = MagicMock(spec=SessionLoggerAdapter)
        
        with patch("logging_config.get_logger", return_value=mock_logger), \
             patch("logging_config.get_session_logger", return_value=mock_session_logger):
            
            # 测试不带会话的操作
            with log_operation(operation="test_operation"):
                pass
            
            mock_logger.info.assert_any_call("Starting operation: test_operation")
            assert "Completed operation: test_operation" in mock_logger.info.call_args_list[-1][0][0]
            
            # 测试带会话的操作
            mock_logger.reset_mock()
            mock_session_logger.reset_mock()
            
            with log_operation(session_key="test-session", operation="test_operation"):
                pass
            
            mock_session_logger.info.assert_any_call("Starting operation: test_operation")
            assert "Completed operation: test_operation" in mock_session_logger.info.call_args_list[-1][0][0]
            
            # 测试异常情况
            mock_session_logger.reset_mock()
            
            with pytest.raises(ValueError):
                with log_operation(session_key="test-session", operation="test_operation"):
                    raise ValueError("Test error")
            
            mock_session_logger.info.assert_any_call("Starting operation: test_operation")
            assert "Failed operation: test_operation" in mock_session_logger.error.call_args_list[0][0][0]


class TestSetupLogging:
    """日志设置函数测试"""
    
    def test_setup_logging(self):
        """测试设置日志系统"""
        mock_manager = MagicMock(spec=LoggingManager)
        
        with patch("logging_config.logging_manager", mock_manager):
            setup_logging()
            mock_manager.initialize.assert_called_once()


class TestIntegration:
    """集成测试"""
    
    def test_file_logging(self):
        """测试文件日志记录"""
        # 创建临时日志文件
        with tempfile.TemporaryDirectory() as temp_dir:
            log_path = os.path.join(temp_dir, "test.log")
            
            # 创建配置
            from ..config import Config, LoggingConfig
            config = Config()
            config.logging = LoggingConfig(
                level="INFO",
                file_path=log_path,
                max_file_size=1024,
                backup_count=1
            )
            
            # 初始化日志系统
            manager = LoggingManager()
            manager.initialize(config)
            
            # 获取日志记录器并记录消息
            logger = manager.get_logger("test_integration")
            logger.info("Test message")
            
            # 检查日志文件是否存在并包含消息
            assert os.path.exists(log_path)
            with open(log_path, "r", encoding="utf-8") as f:
                content = f.read()
                assert "Test message" in content
    
    def test_error_handling_chain(self):
        """测试错误处理链"""
        # 初始化日志系统
        manager = LoggingManager()
        manager.initialize()
        
        # 获取API错误处理器
        api_handler = manager.get_error_handler("api")
        
        # 模拟请求处理过程中的错误
        try:
            # 模拟业务逻辑错误
            raise ValueError("Invalid parameter value")
        except ValueError as e:
            # 处理验证错误
            error_response = api_handler.handle_validation_error(e, {"param": "value"})
            
            # 验证错误响应
            assert error_response["error"] == "Invalid request parameters"
            assert error_response["detail"] == "Invalid parameter value"
            assert error_response["type"] == "validation_error"


if __name__ == "__main__":
    pytest.main(["-v", __file__])