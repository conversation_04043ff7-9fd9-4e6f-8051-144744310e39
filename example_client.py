#!/usr/bin/env python3
"""
RTMP视频处理API客户端示例

本示例演示如何使用RTMP视频处理API的完整流程：
1. 创建处理会话
2. 建立SSE连接接收结果
3. 使用ffmpeg推送RTMP流
4. 计算端到端延迟

使用OpenCVPreviewProcessor进行视频处理演示。
"""

import asyncio
import json
import time
import subprocess
import threading
from datetime import datetime
from typing import Optional, Dict, Any
import sys
import signal

import httpx
import requests
import sseclient


class RTMPVideoProcessingClient:
    """RTMP视频处理API客户端"""
    
    def __init__(self, api_base_url: str = "http://localhost:8000", 
                 rtmp_url: str = "rtmp://localhost:1935"):
        self.api_base_url = api_base_url.rstrip('/')
        self.rtmp_url = rtmp_url.rstrip('/')
        self.session_key: Optional[str] = None
        self.sse_client: Optional[sseclient.SSEClient] = None
        self.ffmpeg_process: Optional[subprocess.Popen] = None
        self.results_received = 0
        self.stream_start_time: Optional[float] = None
        self.first_result_time: Optional[float] = None
        self.running = True
        
        # 设置信号处理
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
    
    def _signal_handler(self, signum, frame):
        """处理退出信号"""
        print(f"\n收到退出信号 {signum}，正在清理...")
        self.running = False
        self.cleanup()
        sys.exit(0)
    
    async def create_session(self, analysis_type: str = "opencv_preview") -> str:
        """
        创建视频处理会话
        
        Args:
            analysis_type: 分析类型，默认为opencv_preview
            
        Returns:
            会话密钥
        """
        print(f"创建会话，分析类型: {analysis_type}")
        
        async with httpx.AsyncClient() as client:
            try:
                response = await client.post(
                    f"{self.api_base_url}/api/session",
                    json={"analysis_type": analysis_type},
                    timeout=10.0
                )
                response.raise_for_status()
                
                data = response.json()
                self.session_key = data["session_key"]
                
                print(f"✓ 会话创建成功")
                print(f"  会话密钥: {self.session_key}")
                print(f"  分析类型: {data['analysis_type']}")
                print(f"  创建时间: {data['created_at']}")
                
                return self.session_key
                
            except httpx.HTTPError as e:
                print(f"✗ 创建会话失败: {e}")
                if hasattr(e, 'response') and e.response:
                    print(f"  错误详情: {e.response.text}")
                raise
    
    async def get_session_status(self) -> Dict[str, Any]:
        """获取会话状态"""
        if not self.session_key:
            raise ValueError("会话未创建")
        
        async with httpx.AsyncClient() as client:
            try:
                response = await client.get(
                    f"{self.api_base_url}/api/session/{self.session_key}",
                    timeout=10.0
                )
                response.raise_for_status()
                return response.json()
                
            except httpx.HTTPError as e:
                print(f"✗ 获取会话状态失败: {e}")
                raise
    
    def start_sse_connection(self) -> threading.Thread:
        """
        启动SSE连接接收处理结果
        
        Returns:
            SSE处理线程
        """
        if not self.session_key:
            raise ValueError("会话未创建")
        
        print("启动SSE连接...")
        
        def sse_handler():
            """SSE事件处理函数"""
            try:
                url = f"{self.api_base_url}/events/{self.session_key}"
                print(f"连接到SSE端点: {url}")
                
                # 使用requests创建SSE连接
                response = requests.get(
                    url, 
                    stream=True,
                    headers={
                        'Accept': 'text/event-stream',
                        'Cache-Control': 'no-cache',
                        'Connection': 'keep-alive'
                    },
                    timeout=(10, None)  # 连接超时10秒，读取无超时
                )
                response.raise_for_status()
                
                self.sse_client = sseclient.SSEClient(response)
                print("✓ SSE连接建立成功")
                
                # 处理SSE事件
                try:
                    for event in self.sse_client.events():
                        if not self.running:
                            print("收到停止信号，退出SSE处理")
                            break
                        
                        try:
                            self._handle_sse_event(event)
                        except Exception as e:
                            print(f"处理单个SSE事件时出错: {e}")
                            
                except (AttributeError, ConnectionError, OSError, TypeError) as e:
                    # 处理连接被关闭的情况
                    if not self.running:
                        print("SSE连接已正常关闭")
                    else:
                        print(f"SSE连接意外关闭: {type(e).__name__}: {e}")
                except Exception as e:
                    if self.running:
                        print(f"SSE事件处理错误: {e}")
                    else:
                        print("SSE连接已正常关闭")
                    
            except requests.exceptions.RequestException as e:
                if self.running:
                    print(f"✗ SSE连接错误: {e}")
                    if hasattr(e, 'response') and e.response:
                        print(f"  错误详情: {e.response.text}")
                else:
                    print("SSE连接已正常关闭")
            except Exception as e:
                if self.running:
                    print(f"✗ SSE处理异常: {e}")
                    import traceback
                    traceback.print_exc()
                else:
                    print("SSE连接已正常关闭")
            finally:
                print("SSE连接处理结束")
        
        # 在独立线程中运行SSE处理
        sse_thread = threading.Thread(target=sse_handler, name="SSE-Handler", daemon=True)
        sse_thread.start()
        
        # 等待连接建立
        time.sleep(2)
        
        return sse_thread
    
    def _handle_sse_event(self, event):
        """处理SSE事件"""
        try:
            if event.event == "connected":
                data = json.loads(event.data)
                print(f"✓ SSE连接确认: {data['session_key']}")
                
            elif event.event == "result":
                # 记录第一个结果的时间
                if self.first_result_time is None:
                    self.first_result_time = time.time()
                    if self.stream_start_time:
                        latency = (self.first_result_time - self.stream_start_time) * 1000
                        print(f"🚀 端到端延迟: {latency:.2f}ms")
                
                self.results_received += 1
                
                # 每10个结果显示一次计数，避免输出过多
                if self.results_received % 10 == 0:
                    print(f"📊 已接收 {self.results_received} 个处理结果")
                
            elif event.event == "heartbeat":
                # 心跳事件，不需要特殊处理
                pass
                
            elif event.event == "error":
                data = json.loads(event.data)
                print(f"✗ 处理错误: {data.get('error', 'Unknown error')}")
                
            elif event.event == "close":
                print("SSE连接已关闭")
                
        except json.JSONDecodeError as e:
            print(f"✗ 解析SSE数据失败: {e}")
        except Exception as e:
            print(f"✗ 处理SSE事件失败: {e}")
    

    
    def start_rtmp_stream(self, input_source: str = "testsrc", duration: int = 30) -> subprocess.Popen:
        """
        启动RTMP流推送
        
        Args:
            input_source: 输入源，可以是文件路径或测试源
            duration: 推流持续时间（秒）
            
        Returns:
            ffmpeg进程
        """
        if not self.session_key:
            raise ValueError("会话未创建")
        
        rtmp_stream_url = f"{self.rtmp_url}/live/{self.session_key}"
        
        print(f"启动RTMP推流...")
        print(f"  输入源: {input_source}")
        print(f"  推流地址: {rtmp_stream_url}")
        print(f"  持续时间: {duration}秒")
        
        # 构建ffmpeg命令
        if input_source == "testsrc":
            # 使用测试源
            cmd = [
                "ffmpeg",
                "-f", "lavfi",
                "-i", f"testsrc=duration={duration}:size=640x480:rate=30",
                "-c:v", "libx264",
                "-preset", "ultrafast",
                "-tune", "zerolatency",
                "-f", "flv",
                rtmp_stream_url
            ]
        elif input_source == "webcam":
            # 使用摄像头（Windows）
            cmd = [
                "ffmpeg",
                "-f", "dshow",
                "-i", "video=HD User Facing",
                "-c:v", "libx264",
                "-preset", "ultrafast",
                "-tune", "zerolatency",
                "-t", str(duration),
                "-f", "flv",
                rtmp_stream_url
            ]
        else:
            # 使用文件
            cmd = [
                "ffmpeg",
                "-re",
                "-i", input_source,
                "-c:v", "libx264",
                "-preset", "ultrafast",
                "-tune", "zerolatency",
                "-t", str(duration),
                "-f", "flv",
                rtmp_stream_url
            ]
        
        try:
            # 记录推流开始时间
            self.stream_start_time = time.time()
            
            # 启动ffmpeg进程
            self.ffmpeg_process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                universal_newlines=True
            )
            
            print(f"✓ RTMP推流已启动 (PID: {self.ffmpeg_process.pid})")
            return self.ffmpeg_process
            
        except FileNotFoundError:
            print("✗ 未找到ffmpeg，请确保已安装ffmpeg并添加到PATH")
            raise
        except Exception as e:
            print(f"✗ 启动RTMP推流失败: {e}")
            raise
    
    def wait_for_stream_completion(self, timeout: int = 60):
        """等待推流完成"""
        if not self.ffmpeg_process:
            return
        
        print("等待推流完成...")
        
        try:
            # 等待进程完成
            stdout, stderr = self.ffmpeg_process.communicate(timeout=timeout)
            
            if self.ffmpeg_process.returncode == 0:
                print("✓ 推流完成")
            else:
                print(f"✗ 推流异常结束 (返回码: {self.ffmpeg_process.returncode})")
                if stderr:
                    print(f"错误信息: {stderr}")
                    
        except subprocess.TimeoutExpired:
            print("⚠ 推流超时，强制终止")
            self.ffmpeg_process.kill()
            self.ffmpeg_process.wait()
    
    def cleanup(self):
        """清理资源"""
        print("清理资源...")
        
        # 设置停止标志
        self.running = False
        
        # 停止ffmpeg进程
        if self.ffmpeg_process and self.ffmpeg_process.poll() is None:
            print("  停止RTMP推流...")
            self.ffmpeg_process.terminate()
            try:
                self.ffmpeg_process.wait(timeout=5)
                print("  ✓ RTMP推流已停止")
            except subprocess.TimeoutExpired:
                print("  ⚠ RTMP推流强制终止")
                self.ffmpeg_process.kill()
                self.ffmpeg_process.wait()
        
        # 关闭SSE连接
        print("  关闭SSE连接...")
        if self.sse_client:
            try:
                # 先关闭底层的HTTP响应
                if hasattr(self.sse_client, '_event_source'):
                    self.sse_client._event_source.close()
                self.sse_client.close()
            except Exception as e:
                # 忽略关闭时的错误，这是正常的
                pass
        
        # 等待SSE线程结束
        print("  等待SSE线程结束...")
        import time
        time.sleep(1)  # 给SSE线程一些时间来处理停止信号
        print("  ✓ SSE连接已关闭")
        
        print("✓ 资源清理完成")
    
    async def delete_session(self):
        """删除会话"""
        if not self.session_key:
            return
        
        print(f"删除会话: {self.session_key}")
        
        async with httpx.AsyncClient() as client:
            try:
                response = await client.delete(
                    f"{self.api_base_url}/api/session/{self.session_key}",
                    timeout=10.0
                )
                response.raise_for_status()
                print("✓ 会话删除成功")
                
            except httpx.HTTPError as e:
                print(f"✗ 删除会话失败: {e}")


async def main():
    """主函数 - 演示完整的API使用流程"""
    print("=" * 60)
    print("RTMP视频处理API客户端示例")
    print("=" * 60)
    
    # 创建客户端
    client = RTMPVideoProcessingClient()
    
    try:
        # 1. 创建会话
        print("\n1. 创建处理会话")
        await client.create_session("opencv_preview")
        
        # 2. 获取会话状态
        print("\n2. 获取会话状态")
        status = await client.get_session_status()
        print(f"✓ 会话状态: {status['status']}")
        print(f"  处理活跃: {status['processing_active']}")
        
        # 3. 启动SSE连接
        print("\n3. 启动SSE连接")
        sse_thread = client.start_sse_connection()
        
        # 4. 启动RTMP推流
        print("\n4. 启动RTMP推流")
        
        # 询问用户输入源
        print("\n选择输入源:")
        print("1. 测试源 (testsrc)")
        print("2. 摄像头 (webcam)")
        print("3. 视频文件")
        
        choice = input("请选择 (1-3, 默认1): ").strip()
        
        if choice == "2":
            input_source = "webcam"
        elif choice == "3":
            file_path = input("请输入视频文件路径: ").strip()
            if not file_path:
                input_source = "testsrc"
            else:
                input_source = file_path
        else:
            input_source = "testsrc"
        
        # 询问推流时长
        duration_input = input("推流时长（秒，默认30）: ").strip()
        try:
            duration = int(duration_input) if duration_input else 30
        except ValueError:
            duration = 30
        
        # 启动推流
        client.start_rtmp_stream(input_source, duration)
        
        # 5. 等待推流完成
        print("\n5. 等待推流完成")
        print(f"  等待最多 {duration + 10} 秒...")
        client.wait_for_stream_completion(duration + 10)
        
        # 6. 等待一段时间接收剩余结果
        print("\n6. 等待接收剩余结果...")
        print("  等待5秒以接收剩余的处理结果...")
        for i in range(5):
            await asyncio.sleep(1)
            print(f"  已等待 {i+1}/5 秒，当前接收到 {client.results_received} 个结果")
        
        # 7. 显示统计信息
        print("\n7. 统计信息")
        print(f"✓ 总共接收到 {client.results_received} 个处理结果")
        
        if client.stream_start_time and client.first_result_time:
            latency = (client.first_result_time - client.stream_start_time) * 1000
            print(f"✓ 端到端延迟: {latency:.2f}ms")
        
        # 8. 清理资源
        print("\n8. 清理资源")
        client.cleanup()
        
        # 9. 删除会话
        print("\n9. 删除会话")
        await client.delete_session()
        
        print("\n" + "=" * 60)
        print("示例执行完成！")
        print("=" * 60)
        
    except KeyboardInterrupt:
        print("\n用户中断执行")
        client.cleanup()
        await client.delete_session()
    except Exception as e:
        print(f"\n✗ 执行过程中发生错误: {e}")
        client.cleanup()
        await client.delete_session()
        raise


if __name__ == "__main__":
    # 检查依赖
    try:
        import httpx
        import requests
        import sseclient
    except ImportError as e:
        print(f"缺少依赖库: {e}")
        print("请安装依赖: pip install httpx requests sseclient-py")
        sys.exit(1)
    
    # 运行示例
    asyncio.run(main())