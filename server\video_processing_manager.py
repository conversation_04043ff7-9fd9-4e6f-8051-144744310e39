"""
视频处理管理器 - 处理RTMP回调和会话协调
"""
import asyncio
import time
from typing import Dict, Optional, Set
import numpy as np

from pyrtmp.rtmp import SimpleRTMPController
from pyrtmp.messages.video import VideoMessage
from pyrtmp.messages.audio import AudioMessage
import av

from models import Session, SessionStatus
from .session_manager import SessionManager
from logging_config import (
    get_logger, get_session_logger, get_error_handler,
    log_errors, log_operation
)

logger = get_logger(__name__)
rtmp_error_handler = get_error_handler("rtmp")


class VideoDecoder:
    """
    视频解码器，用于处理RTMP视频流解码
    """
    def __init__(self, codec_name='h264'):
        self.codec_context = av.CodecContext.create(codec_name, 'r')
        self.decoder_id = id(self)
        logger.info(f"Initialized {codec_name} decoder", extra={'extra_data': {'decoder_id': self.decoder_id}})

    @log_errors("rtmp", "video_decode")
    def decode(self, payload: bytes) -> list[np.ndarray]:
        """
        解码收到的视频数据包。
        :param payload: 来自 RTMP VideoMessage 的原始负载。
        :return: 解码后的视频帧列表 (numpy.ndarray)。
        """
        frames = []
        try:
            # 检查负载长度
            if len(payload) < 2:
                logger.warning("Payload too short for video decoding")
                return frames
            
            # FLV Video Tag: 第1字节 FrameType|CodecID, 第2字节 AVCPacketType
            avc_packet_type = payload[1]
            logger.debug(f"Decoding video packet, AVC packet type: {avc_packet_type}, payload size: {len(payload)}",
                          extra={'extra_data': {'decoder_id': self.decoder_id, 'payload_size': len(payload)}})
            
            if avc_packet_type == 0:  # AVC sequence header
                if len(payload) < 6:
                    logger.warning("AVC sequence header payload too short")
                    return frames
                self.codec_context.extradata = payload[5:]
                logger.debug(f"Set decoder extradata, size: {len(self.codec_context.extradata)}",
                             extra={'extra_data': {'decoder_id': self.decoder_id}})
                return frames
            elif avc_packet_type == 1:  # NALU
                # 从 FLV 格式 (NALU 长度前缀) 转换为 Annex B 格式 (起始码)
                if len(payload) < 6:
                    logger.warning("NALU payload too short")
                    return frames
                    
                annexb_data = b''
                i = 5
                while i + 4 <= len(payload):
                    nalu_size = int.from_bytes(payload[i:i+4], 'big')
                    i += 4
                    if i + nalu_size > len(payload):
                        logger.warning("NALU size exceeds payload size")
                        break
                    nalu = payload[i:i+nalu_size]
                    annexb_data += b'\x00\x00\x00\x01' + nalu
                    i += nalu_size
                
                if not annexb_data:
                    logger.debug("No valid NALU data found")
                    return frames
                    
                logger.debug(f"Decoding NALU data, size: {len(annexb_data)}",
                             extra={'extra_data': {'decoder_id': self.decoder_id}})
                packet = av.Packet(annexb_data)
                decoded_frames = self.codec_context.decode(packet)
                logger.debug(f"Decoded {len(decoded_frames)} frames from codec",
                             extra={'extra_data': {'decoder_id': self.decoder_id}})
                
                for frame in decoded_frames:
                    # 转换为 OpenCV 兼容的 BGR24 格式
                    np_frame = frame.to_ndarray(format='bgr24')
                    frames.append(np_frame)
                    logger.debug(f"Converted frame to numpy array, shape: {np_frame.shape}")
            else:
                # 忽略其他类型
                logger.debug(f"Ignoring AVC packet type: {avc_packet_type}",
                             extra={'extra_data': {'decoder_id': self.decoder_id}})
                return frames
        except Exception as e:
            logger.error(f"Error decoding video: {e}", exc_info=True,
                          extra={'extra_data': {'decoder_id': self.decoder_id, 'payload_size': len(payload)}})
            rtmp_error_handler.handle_decoding_error("unknown", e)
            raise
        
        logger.debug(f"Returning {len(frames)} decoded frames",
                          extra={'extra_data': {'decoder_id': self.decoder_id}})
        return frames


class VideoProcessingManager(SimpleRTMPController):
    """
    视频处理管理器
    
    负责处理RTMP回调和会话协调，但不直接管理帧队列和处理协程。
    每个Session自己管理frame_queue和processing_task。
    继承SimpleRTMPController以直接接收RTMP服务器回调。
    """
    
    def __init__(self, session_manager: SessionManager, connection_timeout: float = 10.0):
        """
        初始化视频处理管理器
        
        Args:
            session_manager: 会话管理器实例
            connection_timeout: 连接超时时间（秒），用于判断连接稳定性
        """
        super().__init__()
        self._session_manager = session_manager
        self._connection_timeout = connection_timeout
        
        # 跟踪活跃的流连接
        self._active_streams: Set[str] = set()
        
        # 跟踪连接状态，用于判断连接稳定性
        self._connection_states: Dict[str, Dict] = {}
        
        # 每个流的解码器
        self._decoders: Dict[str, VideoDecoder] = {}
        
        # 映射session到publishing_name
        self._session_to_stream: Dict[object, str] = {}
        
        logger.info(f"VideoProcessingManager initialized with connection_timeout={connection_timeout}s")
    
    # RTMP Controller methods
    @log_errors("rtmp", "rtmp_publish")
    async def on_ns_publish(self, session, message) -> None:
        """当客户端开始推流时调用（RTMP回调）"""
        publishing_name = message.publishing_name
        
        with log_operation(session_key=publishing_name, operation="rtmp_publish", logger_name="rtmp.publish"):
            # 验证会话密钥并启动流处理
            # 使用publishing_name作为session_key进行验证
            if await self.on_stream_start(publishing_name):
                try:
                    # 为这个流创建解码器
                    self._decoders[publishing_name] = VideoDecoder()
                    # 映射session到publishing_name
                    self._session_to_stream[session] = publishing_name
                    await super().on_ns_publish(session, message)
                    logger.info(f"RTMP stream accepted: {publishing_name}")
                except Exception as e:
                    rtmp_error_handler.handle_connection_error(publishing_name, e)
                    session.close()
            else:
                rtmp_error_handler.handle_stream_validation_error(publishing_name, "Session validation failed")
                session.close()
    
    @log_errors("rtmp", "rtmp_video_message")
    async def on_video_message(self, session, message: VideoMessage) -> None:
        """处理RTMP视频消息"""
        # 从session映射中获取publishing_name
        publishing_name = self._session_to_stream.get(session)
        
        if not publishing_name:
            logger.warning("Received video message for unknown session")
            return
        
        if publishing_name not in self._decoders:
            logger.warning(f"No decoder found for stream: {publishing_name}")
            return
        
        try:
            decoder = self._decoders[publishing_name]
            frames = decoder.decode(message.payload)
            logger.debug(f"Decoded {len(frames)} frames from stream: {publishing_name}")
            
            for i, frame in enumerate(frames):
                # 处理解码后的帧
                await self._process_decoded_frame(publishing_name, frame)
                logger.debug(f"Processed frame {i+1}/{len(frames)} for stream: {publishing_name}")
        except Exception as e:
            logger.error(f"Error processing video message for stream {publishing_name}: {e}", exc_info=True)
            rtmp_error_handler.handle_decoding_error(publishing_name, e)
    
    @log_errors("rtmp", "process_decoded_frame")
    async def _process_decoded_frame(self, stream_key: str, frame: np.ndarray) -> None:
        """
        处理解码后的视频帧
        
        Args:
            stream_key: 流密钥
            frame: 解码后的视频帧
        """
        session_logger = get_session_logger(stream_key, "process_frame", "rtmp.frame")
        
        # 检查流是否活跃
        if stream_key not in self._active_streams:
            session_logger.warning(f"Received frame for inactive stream")
            return
        
        # 获取对应的会话
        session = self._session_manager.get_session(stream_key)
        if not session:
            session_logger.warning(f"Received frame for non-existent session")
            await self._cleanup_stream_async(stream_key)
            return
        
        try:
            # 将帧放入会话的帧队列（非阻塞）
            session.frame_queue.put_frame(frame)
            
            # 更新连接状态
            if stream_key in self._connection_states:
                state = self._connection_states[stream_key]
                state["last_frame_time"] = time.time()
                state["frame_count"] += 1
            
            # 更新会话活动时间
            session.update_activity()
            
            session_logger.debug(f"Put frame into session queue", extra={'extra_data': {
                'frame_shape': frame.shape,
                'frame_count': self._connection_states.get(stream_key, {}).get('frame_count', 0)
            }})
            
        except Exception as e:
            session_logger.error(f"Error processing decoded frame: {e}", exc_info=True)
            rtmp_error_handler.handle_decoding_error(stream_key, e)
    
    async def on_audio_message(self, session, message: AudioMessage) -> None:
        """忽略音频消息"""
        pass
    
    async def on_stream_closed(self, session, exception) -> None:
        """当客户端停止推流时调用（RTMP回调）"""
        publishing_name = None
        # 清理session映射
        if session in self._session_to_stream:
            publishing_name = self._session_to_stream.get(session)
            del self._session_to_stream[session]

        logger.info(f"RTMP unpublish for: {publishing_name}")
        
        # 清理解码器
        if publishing_name in self._decoders:
            del self._decoders[publishing_name]
        
        # 处理流结束
        await self.on_stream_end(publishing_name)
        await super().on_stream_closed(session, exception)
    
    async def on_stream_start(self, stream_key: str) -> bool:
        """
        RTMP流开始回调，验证会话并启动Session的处理协程
        
        Args:
            stream_key: 流密钥（应该对应session_key）
            
        Returns:
            bool: 是否允许连接
        """
        logger.info(f"Stream start requested for key: {stream_key}")
        
        # 使用stream_key作为session_key查找会话
        session = self._session_manager.get_session(stream_key)
        if not session:
            logger.warning(f"Stream rejected: session not found for key {stream_key}")
            return False
        
        # 检查会话状态
        if session.status == SessionStatus.STOPPED or session.status == SessionStatus.ERROR:
            logger.warning(f"Stream rejected: session {stream_key} is in {session.status.value} state")
            return False
        
        try:
            # 启动会话的帧处理协程
            session.start_processing()
            
            # 记录活跃流
            self._active_streams.add(stream_key)
            
            # 初始化连接状态
            self._connection_states[stream_key] = {
                "start_time": time.time(),
                "last_frame_time": time.time(),
                "frame_count": 0,
                "disconnection_count": 0,
                "last_disconnection_time": None
            }
            
            logger.info(f"Stream started successfully for session {stream_key}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to start stream for session {stream_key}: {e}")
            return False
    

    
    async def on_stream_end(self, stream_key: str) -> None:
        """
        RTMP流结束回调，处理连接状态判断
        
        Args:
            stream_key: 流密钥
        """
        logger.info(f"Stream end event for key: {stream_key}")
        
        # 更新连接状态
        if stream_key in self._connection_states:
            state = self._connection_states[stream_key]
            state["disconnection_count"] += 1
            state["last_disconnection_time"] = time.time()
        
        # 判断是否应该结束会话
        if self._should_end_session(stream_key):
            logger.info(f"Ending session for stream {stream_key}")
            await self._end_session_async(stream_key)
        else:
            logger.info(f"Keeping session alive for stream {stream_key} (temporary disconnection)")
            # 从活跃流中移除，但保持会话
            self._active_streams.discard(stream_key)
    
    def _should_end_session(self, stream_key: str) -> bool:
        """
        判断是否应该结束会话（区分连接不稳定和真正结束）
        
        Args:
            stream_key: 流密钥
            
        Returns:
            bool: 是否应该结束会话
        """
        if stream_key not in self._connection_states:
            # 没有连接状态记录，直接结束
            return True
        
        state = self._connection_states[stream_key]
        current_time = time.time()
        
        # 检查会话是否刚开始就断开（可能是配置错误）
        session_duration = current_time - state["start_time"]
        if session_duration < 5.0 and state["frame_count"] < 10:
            logger.info(f"Session {stream_key} ended quickly with few frames, likely configuration issue")
            return True
        
        # 检查断开频率
        if state["disconnection_count"] > 5:
            logger.info(f"Session {stream_key} has too many disconnections ({state['disconnection_count']})")
            return True
        
        # 检查最后一次接收帧的时间
        time_since_last_frame = current_time - state["last_frame_time"]
        if time_since_last_frame > self._connection_timeout:
            logger.info(f"Session {stream_key} has been inactive for {time_since_last_frame:.1f}s")
            return True
        
        # 如果是短时间内的断开，可能是网络不稳定
        if state["last_disconnection_time"]:
            time_since_last_disconnection = current_time - state["last_disconnection_time"]
            if time_since_last_disconnection < 2.0:  # 2秒内的断开认为是临时的
                logger.info(f"Recent disconnection for {stream_key}, treating as temporary")
                return False
        
        # 默认情况下结束会话
        return True
    
    async def _end_session_async(self, stream_key: str) -> None:
        """
        结束会话并清理资源
        
        Args:
            stream_key: 流密钥
        """
        try:
            # 获取会话
            session = self._session_manager.get_session(stream_key)
            if session:
                # 停止会话处理
                session.stop_processing()
                logger.info(f"Stopped processing for session {stream_key}")
            
            # 从会话管理器中移除会话
            self._session_manager.remove_session(stream_key)
            
            # 清理本地状态
            await self._cleanup_stream_async(stream_key)
            
        except Exception as e:
            logger.error(f"Error ending session {stream_key}: {e}")
    
    async def _cleanup_stream_async(self, stream_key: str) -> None:
        """
        清理流相关的本地状态
        
        Args:
            stream_key: 流密钥
        """
        self._active_streams.discard(stream_key)
        self._connection_states.pop(stream_key, None)
        self._decoders.pop(stream_key, None)
        
        # 清理session映射中对应的条目
        sessions_to_remove = [session for session, name in self._session_to_stream.items() if name == stream_key]
        for session in sessions_to_remove:
            del self._session_to_stream[session]
        
        logger.debug(f"Cleaned up stream state for {stream_key}")
    
    def get_active_streams(self) -> Set[str]:
        """
        获取当前活跃的流列表
        
        Returns:
            Set[str]: 活跃流的密钥集合
        """
        return self._active_streams.copy()
    
    def get_stream_stats(self, stream_key: str) -> Optional[Dict]:
        """
        获取流的统计信息
        
        Args:
            stream_key: 流密钥
            
        Returns:
            Optional[Dict]: 流统计信息，如果流不存在则返回None
        """
        if stream_key not in self._connection_states:
            return None
        
        state = self._connection_states[stream_key]
        current_time = time.time()
        
        return {
            "stream_key": stream_key,
            "is_active": stream_key in self._active_streams,
            "start_time": state["start_time"],
            "duration": current_time - state["start_time"],
            "frame_count": state["frame_count"],
            "last_frame_time": state["last_frame_time"],
            "time_since_last_frame": current_time - state["last_frame_time"],
            "disconnection_count": state["disconnection_count"],
            "last_disconnection_time": state.get("last_disconnection_time")
        }
    
    async def cleanup_all_streams(self) -> None:
        """清理所有流状态"""
        stream_keys = list(self._active_streams)
        for stream_key in stream_keys:
            await self._cleanup_stream_async(stream_key)
        logger.info(f"Cleaned up all {len(stream_keys)} active streams")