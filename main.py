"""
主应用程序入口
集成所有组件并启动服务
实现RTMP服务器和FastAPI服务器的并发启动
添加优雅关闭机制，确保资源正确清理
"""
import asyncio
import signal
import sys
import threading
import uvicorn
from pathlib import Path
from typing import Optional

from config import load_config, get_config
from logging_config import setup_logging, get_logger, log_operation
from server.rtmp_server import RtmpServerManager
from server.video_processing_manager import VideoProcessingManager
from server.session_manager import session_manager
from server.api_server import app


class ApplicationManager:
    """应用程序管理器，负责协调所有组件的启动和关闭"""
    
    def __init__(self):
        self.logger = None
        self.rtmp_server: Optional[RtmpServerManager] = None
        self.video_processing_manager: Optional[VideoProcessingManager] = None
        self.fastapi_server: Optional[uvicorn.Server] = None
        self.fastapi_thread: Optional[threading.Thread] = None
        self.shutdown_event: Optional[asyncio.Event] = None
        self.config = None
        
    def ensure_log_directory(self):
        """确保日志目录存在"""
        if self.config and self.config.logging.file_path:
            log_dir = Path(self.config.logging.file_path).parent
            log_dir.mkdir(parents=True, exist_ok=True)
            # 使用print而不是logger，因为此时logger可能还未初始化
            print(f"Log directory ensured: {log_dir}")

    def init_logging(self):
        """初始化日志系统"""
        # 只有在配置未设置时才加载配置
        if self.config is None:
            self.config = load_config("config.toml", use_env=True)
        
        # 确保日志目录存在
        self.ensure_log_directory()
        
        # 设置日志系统
        setup_logging(self.config)
        
        # 获取日志记录器
        self.logger = get_logger(__name__)
        self.logger.info("Logging system initialized successfully")

    def setup_signal_handlers(self):
        """设置信号处理器"""
        def handle_exit(sig, frame):
            """处理退出信号"""
            if self.shutdown_event is None:
                return
            
            self.logger.info(f"Received exit signal: {sig}")
            # 在事件循环中设置关闭事件
            try:
                loop = asyncio.get_event_loop()
                loop.call_soon_threadsafe(self.shutdown_event.set)
            except RuntimeError:
                # 如果没有运行的事件循环，直接退出
                sys.exit(0)

        # 注册信号处理器
        for sig in (signal.SIGINT, signal.SIGTERM):
            signal.signal(sig, handle_exit)
        
        self.logger.info("Signal handlers registered")

    def create_components(self):
        """创建所有组件"""
        with log_operation(operation="create_components", logger_name="app.startup"):
            # 更新会话管理器配置
            session_manager.update_config(
                session_timeout=self.config.session.timeout,
                max_concurrent_sessions=self.config.session.max_concurrent_sessions
            )
            
            # 创建VideoProcessingManager，传入session_manager
            self.video_processing_manager = VideoProcessingManager(
                session_manager=session_manager,
                connection_timeout=self.config.session.timeout
            )
            
            # 创建RTMP服务器
            self.rtmp_server = RtmpServerManager(
                host=self.config.rtmp.host,
                port=self.config.rtmp.port,
                controller=self.video_processing_manager
            )
            
            self.logger.info("All components created successfully")

    def run_fastapi_server(self):
        """在独立线程中运行FastAPI服务器"""
        try:
            # 创建新的事件循环用于FastAPI
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            # 创建uvicorn配置
            config = uvicorn.Config(
                app=app,  # 直接使用app对象而不是字符串
                host=self.config.fastapi.host,
                port=self.config.fastapi.port,
                log_level=self.config.fastapi.log_level,
                loop="asyncio",
                access_log=True
            )
            
            self.fastapi_server = uvicorn.Server(config)
            
            # 运行服务器
            loop.run_until_complete(self.fastapi_server.serve())
            
        except Exception as e:
            self.logger.error(f"FastAPI server error: {e}", exc_info=True)
        finally:
            # 确保事件循环正确关闭
            try:
                loop = asyncio.get_event_loop()
                if not loop.is_closed():
                    # 取消所有待处理的任务
                    pending_tasks = [task for task in asyncio.all_tasks(loop) if not task.done()]
                    if pending_tasks:
                        self.logger.info(f"Cancelling {len(pending_tasks)} pending tasks")
                        for task in pending_tasks:
                            task.cancel()
                        # 等待任务取消完成
                        loop.run_until_complete(asyncio.gather(*pending_tasks, return_exceptions=True))
                    
                    # 关闭事件循环
                    loop.close()
            except Exception as e:
                self.logger.warning(f"Error during FastAPI event loop cleanup: {e}")
            
            self.logger.info("FastAPI server thread finished")

    async def start_services(self):
        """启动所有服务"""
        with log_operation(operation="start_services", logger_name="app.startup"):
            try:
                # 启动会话管理器
                self.logger.info("Starting session manager...")
                session_manager.start()
                
                # 启动RTMP服务器
                self.logger.info(f"Starting RTMP server on {self.config.rtmp.host}:{self.config.rtmp.port}...")
                self.rtmp_server.start()
                
                # 等待RTMP服务器启动
                await asyncio.sleep(1)
                
                # 在独立线程中启动FastAPI服务器
                self.logger.info(f"Starting FastAPI server on {self.config.fastapi.host}:{self.config.fastapi.port}...")
                self.fastapi_thread = threading.Thread(
                    target=self.run_fastapi_server,
                    name="FastAPI-Server",
                    daemon=True
                )
                self.fastapi_thread.start()
                
                # 等待FastAPI服务器启动
                await asyncio.sleep(2)
                
                self.logger.info("All services started successfully")
                self.logger.info("=" * 60)
                self.logger.info("RTMP Video Processing API is running")
                self.logger.info(f"RTMP Server: rtmp://{self.config.rtmp.host}:{self.config.rtmp.port}")
                self.logger.info(f"API Server: http://{self.config.fastapi.host}:{self.config.fastapi.port}")
                self.logger.info(f"API Documentation: http://{self.config.fastapi.host}:{self.config.fastapi.port}/docs")
                self.logger.info("Press Ctrl+C to stop")
                self.logger.info("=" * 60)
                
            except Exception as e:
                self.logger.error(f"Failed to start services: {e}", exc_info=True)
                raise

    async def shutdown_services(self):
        """优雅关闭所有服务"""
        with log_operation(operation="shutdown_services", logger_name="app.shutdown"):
            if self.logger:
                self.logger.info("Initiating graceful shutdown...")
            else:
                print("Initiating graceful shutdown...")
            
            # 1. 首先停止FastAPI服务器，避免新的请求进入
            if self.fastapi_server:
                if self.logger:
                    self.logger.info("Stopping FastAPI server...")
                else:
                    print("Stopping FastAPI server...")
                try:
                    # 设置退出标志
                    self.fastapi_server.should_exit = True
                    if hasattr(self.fastapi_server, 'force_exit'):
                        self.fastapi_server.force_exit = True
                    
                    # 等待FastAPI线程结束，给更多时间
                    if self.fastapi_thread and self.fastapi_thread.is_alive():
                        self.fastapi_thread.join(timeout=10)
                        if self.fastapi_thread.is_alive():
                            if self.logger:
                                self.logger.warning("FastAPI thread did not stop gracefully within timeout")
                            else:
                                print("FastAPI thread did not stop gracefully within timeout")
                        else:
                            if self.logger:
                                self.logger.info("FastAPI server stopped successfully")
                            else:
                                print("FastAPI server stopped successfully")
                        
                except Exception as e:
                    if self.logger:
                        self.logger.error(f"Error stopping FastAPI server: {e}")
                    else:
                        print(f"Error stopping FastAPI server: {e}")
            
            # 2. 停止RTMP服务器
            if self.rtmp_server and self.rtmp_server.is_running:
                if self.logger:
                    self.logger.info("Stopping RTMP server...")
                else:
                    print("Stopping RTMP server...")
                try:
                    self.rtmp_server.stop()
                    # 给RTMP服务器一些时间来清理
                    await asyncio.sleep(1)
                    if self.logger:
                        self.logger.info("RTMP server stopped successfully")
                    else:
                        print("RTMP server stopped successfully")
                except Exception as e:
                    if self.logger:
                        self.logger.error(f"Error stopping RTMP server: {e}")
                    else:
                        print(f"Error stopping RTMP server: {e}")
            
            # 3. 最后停止会话管理器
            if session_manager:
                if self.logger:
                    self.logger.info("Stopping session manager...")
                else:
                    print("Stopping session manager...")
                try:
                    await session_manager.stop()
                    if self.logger:
                        self.logger.info("Session manager stopped successfully")
                    else:
                        print("Session manager stopped successfully")
                except Exception as e:
                    if self.logger:
                        self.logger.error(f"Error stopping session manager: {e}")
                    else:
                        print(f"Error stopping session manager: {e}")
            
            if self.logger:
                self.logger.info("All services stopped successfully")
            else:
                print("All services stopped successfully")

    async def run(self):
        """运行应用程序"""
        try:
            # 初始化日志系统
            self.init_logging()
            
            self.logger.info("Starting RTMP Video Processing API...")
            
            # 创建关闭事件
            self.shutdown_event = asyncio.Event()
            
            # 设置信号处理器
            self.setup_signal_handlers()
            
            # 创建组件
            self.create_components()
            
            # 启动服务
            await self.start_services()
            
            # 等待关闭信号
            await self.shutdown_event.wait()
            
            # 优雅关闭
            await self.shutdown_services()
            
        except KeyboardInterrupt:
            self.logger.info("Received keyboard interrupt")
            await self.shutdown_services()
        except Exception as e:
            if self.logger:
                self.logger.error(f"Application error: {e}", exc_info=True)
            else:
                print(f"Critical error during startup: {e}")
            await self.shutdown_services()
            sys.exit(1)


async def main():
    """主函数"""
    app_manager = ApplicationManager()
    await app_manager.run()


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\nApplication interrupted by user")
        sys.exit(0)
    except Exception as e:
        print(f"Fatal error: {e}")
        sys.exit(1)