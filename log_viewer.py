#!/usr/bin/env python3
"""
日志查看和分析工具
用于查看和分析结构化日志文件
"""

import json
import argparse
import sys
from datetime import datetime
from typing import Dict, Any, List, Optional
from pathlib import Path
import re


class LogViewer:
    """日志查看器"""
    
    def __init__(self, log_file: str):
        self.log_file = Path(log_file)
        if not self.log_file.exists():
            raise FileNotFoundError(f"Log file not found: {log_file}")
    
    def read_logs(self) -> List[Dict[str, Any]]:
        """读取日志文件"""
        logs = []
        with open(self.log_file, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                try:
                    log_entry = json.loads(line.strip())
                    logs.append(log_entry)
                except json.JSONDecodeError as e:
                    print(f"Warning: Invalid JSON at line {line_num}: {e}", file=sys.stderr)
        return logs
    
    def filter_logs(self, logs: List[Dict[str, Any]], 
                   level: Optional[str] = None,
                   session_key: Optional[str] = None,
                   operation: Optional[str] = None,
                   keyword: Optional[str] = None,
                   start_time: Optional[str] = None,
                   end_time: Optional[str] = None) -> List[Dict[str, Any]]:
        """过滤日志"""
        filtered_logs = logs
        
        # 按级别过滤
        if level:
            level = level.upper()
            filtered_logs = [log for log in filtered_logs if log.get('level') == level]
        
        # 按会话密钥过滤
        if session_key:
            filtered_logs = [log for log in filtered_logs if log.get('session_key') == session_key]
        
        # 按操作类型过滤
        if operation:
            filtered_logs = [log for log in filtered_logs if log.get('operation') == operation]
        
        # 按关键字过滤
        if keyword:
            filtered_logs = [log for log in filtered_logs if keyword.lower() in log.get('message', '').lower()]
        
        # 按时间范围过滤
        if start_time or end_time:
            filtered_logs = self._filter_by_time(filtered_logs, start_time, end_time)
        
        return filtered_logs
    
    def _filter_by_time(self, logs: List[Dict[str, Any]], 
                       start_time: Optional[str], 
                       end_time: Optional[str]) -> List[Dict[str, Any]]:
        """按时间范围过滤日志"""
        filtered_logs = []
        
        start_dt = None
        end_dt = None
        
        if start_time:
            try:
                start_dt = datetime.fromisoformat(start_time)
            except ValueError:
                print(f"Warning: Invalid start time format: {start_time}", file=sys.stderr)
                return logs
        
        if end_time:
            try:
                end_dt = datetime.fromisoformat(end_time)
            except ValueError:
                print(f"Warning: Invalid end time format: {end_time}", file=sys.stderr)
                return logs
        
        for log in logs:
            try:
                log_time = datetime.fromisoformat(log.get('timestamp', ''))
                
                if start_dt and log_time < start_dt:
                    continue
                
                if end_dt and log_time > end_dt:
                    continue
                
                filtered_logs.append(log)
            except ValueError:
                # 如果时间戳格式不正确，跳过该日志
                continue
        
        return filtered_logs
    
    def format_log(self, log: Dict[str, Any], detailed: bool = False) -> str:
        """格式化单条日志"""
        timestamp = log.get('timestamp', '')
        level = log.get('level', '')
        logger = log.get('logger', '')
        message = log.get('message', '')
        
        # 基本格式
        formatted = f"[{timestamp}] {level:8} [{logger}] {message}"
        
        # 详细格式
        if detailed:
            session_key = log.get('session_key')
            operation = log.get('operation')
            extra = log.get('extra')
            exception = log.get('exception')
            
            if session_key:
                formatted += f"\n  Session: {session_key}"
            
            if operation:
                formatted += f"\n  Operation: {operation}"
            
            if extra:
                formatted += f"\n  Extra: {extra}"
            
            if exception:
                formatted += f"\n  Exception: {exception.get('type', '')}: {exception.get('message', '')}"
        
        return formatted
    
    def print_logs(self, logs: List[Dict[str, Any]], detailed: bool = False, limit: Optional[int] = None):
        """打印日志"""
        if limit:
            logs = logs[-limit:]  # 只显示最后N条日志
        
        for log in logs:
            print(self.format_log(log, detailed))
            if detailed:
                print()  # 详细模式下添加空行分隔
    
    def get_statistics(self, logs: List[Dict[str, Any]]) -> Dict[str, Any]:
        """获取日志统计信息"""
        stats = {
            'total_logs': len(logs),
            'level_counts': {},
            'logger_counts': {},
            'session_counts': {},
            'operation_counts': {},
            'first_log_time': None,
            'last_log_time': None
        }
        
        if not logs:
            return stats
        
        # 初始化时间
        try:
            stats['first_log_time'] = datetime.fromisoformat(logs[0].get('timestamp', ''))
            stats['last_log_time'] = datetime.fromisoformat(logs[-1].get('timestamp', ''))
        except ValueError:
            pass
        
        # 统计各级别日志数量
        for log in logs:
            level = log.get('level', '')
            stats['level_counts'][level] = stats['level_counts'].get(level, 0) + 1
            
            logger = log.get('logger', '')
            stats['logger_counts'][logger] = stats['logger_counts'].get(logger, 0) + 1
            
            session_key = log.get('session_key')
            if session_key:
                stats['session_counts'][session_key] = stats['session_counts'].get(session_key, 0) + 1
            
            operation = log.get('operation')
            if operation:
                stats['operation_counts'][operation] = stats['operation_counts'].get(operation, 0) + 1
        
        return stats
    
    def print_statistics(self, stats: Dict[str, Any]):
        """打印统计信息"""
        print("=== 日志统计信息 ===")
        print(f"总日志数: {stats['total_logs']}")
        
        if stats['first_log_time'] and stats['last_log_time']:
            print(f"时间范围: {stats['first_log_time']} 到 {stats['last_log_time']}")
        
        print("\n日志级别分布:")
        for level, count in sorted(stats['level_counts'].items()):
            print(f"  {level}: {count}")
        
        print("\n记录器分布:")
        for logger, count in sorted(stats['logger_counts'].items(), key=lambda x: x[1], reverse=True)[:10]:
            print(f"  {logger}: {count}")
        
        if stats['session_counts']:
            print("\n会话分布 (前10):")
            for session, count in sorted(stats['session_counts'].items(), key=lambda x: x[1], reverse=True)[:10]:
                print(f"  {session}: {count}")
        
        if stats['operation_counts']:
            print("\n操作分布:")
            for operation, count in sorted(stats['operation_counts'].items(), key=lambda x: x[1], reverse=True):
                print(f"  {operation}: {count}")


def main():
    parser = argparse.ArgumentParser(description='日志查看和分析工具')
    parser.add_argument('log_file', help='日志文件路径')
    parser.add_argument('-l', '--level', help='过滤日志级别 (DEBUG, INFO, WARNING, ERROR, CRITICAL)')
    parser.add_argument('-s', '--session', help='过滤会话密钥')
    parser.add_argument('-o', '--operation', help='过滤操作类型')
    parser.add_argument('-k', '--keyword', help='过滤关键字')
    parser.add_argument('--start-time', help='开始时间 (ISO格式)')
    parser.add_argument('--end-time', help='结束时间 (ISO格式)')
    parser.add_argument('-d', '--detailed', action='store_true', help='详细模式')
    parser.add_argument('-n', '--limit', type=int, help='限制显示的日志条数')
    parser.add_argument('--stats', action='store_true', help='显示统计信息')
    
    args = parser.parse_args()
    
    try:
        viewer = LogViewer(args.log_file)
        logs = viewer.read_logs()
        
        # 过滤日志
        filtered_logs = viewer.filter_logs(
            logs,
            level=args.level,
            session_key=args.session,
            operation=args.operation,
            keyword=args.keyword,
            start_time=args.start_time,
            end_time=args.end_time
        )
        
        # 显示统计信息
        if args.stats:
            stats = viewer.get_statistics(filtered_logs)
            viewer.print_statistics(stats)
            print()
        
        # 显示日志
        viewer.print_logs(filtered_logs, detailed=args.detailed, limit=args.limit)
        
    except FileNotFoundError as e:
        print(f"Error: {e}", file=sys.stderr)
        sys.exit(1)
    except Exception as e:
        print(f"Error: {e}", file=sys.stderr)
        sys.exit(1)


if __name__ == '__main__':
    main()