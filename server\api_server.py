"""
FastAPI REST接口实现
"""
import asyncio
from typing import Dict, Any, Optional, List
from datetime import datetime
import uuid

from fastapi import FastAPI, HTTPException, status, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import StreamingResponse
from pydantic import BaseModel, Field


from .session_manager import session_manager, ProcessorFactory
from logging_config import (
    get_logger, get_session_logger, get_error_handler,
    log_errors, log_operation, setup_logging
)

# 初始化日志系统
setup_logging()
logger = get_logger(__name__)
api_error_handler = get_error_handler("api")
sse_error_handler = get_error_handler("sse")


# Pydantic模型用于请求验证
class SessionCreateRequest(BaseModel):
    """创建会话请求模型"""
    analysis_type: str = Field(..., description="分析类型", json_schema_extra={"example": "opencv_preview"})


class SessionCreateResponse(BaseModel):
    """创建会话响应模型"""
    session_key: str = Field(..., description="会话密钥")
    analysis_type: str = Field(..., description="分析类型")
    created_at: str = Field(..., description="创建时间")


class SessionStatusResponse(BaseModel):
    """会话状态响应模型"""
    session_key: str = Field(..., description="会话密钥")
    analysis_type: str = Field(..., description="分析类型")
    status: str = Field(..., description="会话状态")
    created_at: str = Field(..., description="创建时间")
    last_activity: str = Field(..., description="最后活动时间")
    processing_active: bool = Field(..., description="是否正在处理")


class ErrorResponse(BaseModel):
    """错误响应模型"""
    error: str = Field(..., description="错误信息")
    detail: Optional[str] = Field(None, description="详细错误信息")


class ProcessorTypesResponse(BaseModel):
    """处理器类型响应模型"""
    available_types: List[str] = Field(..., description="可用的处理器类型列表")


# 创建FastAPI应用
app = FastAPI(
    title="RTMP Video Processing API",
    description="实时视频流处理系统API",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 在生产环境中应该限制具体的域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)




@app.get("/", response_model=Dict[str, str])
@log_errors("api", "root_endpoint")
async def root():
    """根路径，返回API信息"""
    logger.info("Root endpoint accessed")
    return {
        "message": "RTMP Video Processing API",
        "version": "1.0.0",
        "docs": "/docs"
    }


@app.get("/health", response_model=Dict[str, Any])
@log_errors("api", "health_check")
async def health_check():
    """健康检查端点"""
    with log_operation(operation="health_check", logger_name="api.health"):
        session_count = session_manager.get_session_count()
        logger.info(f"Health check performed, active sessions: {session_count}")
        return {
            "status": "healthy",
            "timestamp": datetime.now().isoformat(),
            "active_sessions": session_count
        }


@app.get("/api/processors", response_model=ProcessorTypesResponse)
@log_errors("api", "get_processor_types")
async def get_processor_types():
    """获取所有可用的处理器类型"""
    with log_operation(operation="get_processor_types", logger_name="api.processors"):
        try:
            available_types = ProcessorFactory.get_available_types()
            logger.info(f"Retrieved {len(available_types)} processor types: {available_types}")
            return ProcessorTypesResponse(available_types=available_types)
        except Exception as e:
            error_response = api_error_handler.handle_internal_error(e, "get_processor_types")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=error_response["detail"]
            )


@app.post("/api/session", response_model=SessionCreateResponse)
async def create_session(request: SessionCreateRequest):
    """
    创建新的视频处理会话
    
    根据需求1.1, 1.2, 1.3:
    - 生成唯一的会话ID
    - 根据analysis_type参数创建相应的处理器实例
    - 返回包含session_key的JSON响应
    """
    try:
        # 验证处理器类型是否支持
        available_types = ProcessorFactory.get_available_types()
        if request.analysis_type not in available_types:
            detail=api_error_handler.handle_validation_error(
                ValueError(f"Unsupported analysis_type: {request.analysis_type}, "
                + f"available types: {available_types}"), 
                request.model_dump()
                )
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=detail
            )
        
        # 创建会话
        session_key = session_manager.create_session(request.analysis_type)
        
        logger.info(f"Created session {session_key} with analysis_type={request.analysis_type}")
        
        return SessionCreateResponse(
            session_key=session_key,
            analysis_type=request.analysis_type,
            created_at=datetime.now().isoformat()
        )
        
    except HTTPException:
        # 重新抛出HTTPException，不要包装它
        raise
    except ValueError as e:
        # 处理会话创建失败的情况（如达到最大会话数）
        logger.warning(f"Session creation failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Unexpected error creating session: {e}")
        detail = api_error_handler.handle_internal_error(e, "create_session")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=detail
        )


@app.get("/api/session/{session_key}", response_model=SessionStatusResponse)
async def get_session_status(session_key: str):
    """
    获取会话状态信息
    
    根据需求5.5: 提供会话状态查询的接口
    """
    try:
        session_status = session_manager.get_session_status(session_key)
        
        if session_status is None:
            detail = api_error_handler.handle_session_not_found(session_key)
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail = detail
            )
        
        return SessionStatusResponse(**session_status)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting session status for {session_key}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )


@app.delete("/api/session/{session_key}", response_model=Dict[str, str])
async def delete_session(session_key: str):
    """
    删除会话
    
    根据需求5.1, 5.3: 支持会话的清理和资源释放
    """
    try:
        success = session_manager.remove_session(session_key)
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Session not found: {session_key}"
            )
        
        logger.info(f"Deleted session {session_key}")
        return {"message": f"Session {session_key} deleted successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting session {session_key}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )


@app.get("/api/sessions", response_model=List[SessionStatusResponse])
async def list_sessions():
    """
    列出所有会话的状态信息
    
    用于系统监控和调试
    """
    try:
        sessions = session_manager.list_sessions()
        return [SessionStatusResponse(**session) for session in sessions]
        
    except Exception as e:
        logger.error(f"Error listing sessions: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )


# 错误处理器
@app.exception_handler(ValueError)
async def value_error_handler(request, exc):
    """处理ValueError异常"""
    logger.warning(f"ValueError: {exc}", extra={'extra_data': {'url': str(request.url)}})
    return HTTPException(
        status_code=status.HTTP_400_BAD_REQUEST,
        detail=str(exc)
    )


@app.get("/events/{session_key}")
@log_errors("sse", "sse_events")
async def sse_events(request: Request, session_key: str):
    """
    建立SSE连接，实时推送处理结果
    
    根据需求2.1, 2.2, 2.3, 2.4, 2.5, 5.2:
    - 建立SSE连接
    - 根据session_key找到对应的通信队列
    - 当通信队列中有新的处理结果时，立即通过SSE发送给客户端
    - 优雅地处理客户端断开连接
    - 如果session_key不存在，返回404错误
    """
    # 创建会话日志记录器
    session_logger = get_session_logger(session_key, "sse_events", "sse")
    
    try:
        # 获取会话实例
        session = session_manager.get_session(session_key)
        
        if not session:
            error_msg = f"Session not found: {session_key}"
            session_logger.warning(error_msg)
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=error_msg
            )
        
        # 更新会话状态
        session.update_activity()
        session_logger.info("SSE connection established")
        
        # 创建SSE事件流
        async def event_generator():
            """生成SSE事件流"""
            import json
            import time

            # 发送初始连接成功消息
            yield "event: connected\ndata: {\"session_key\": \"" + session_key + "\"}\n\n"

            last_heartbeat = time.time()
            heartbeat_interval = 30.0  # 30秒发送一次心跳
            disconnect_check_counter = 0
            disconnect_check_interval = 50  # 每50次循环检查一次客户端连接

            try:
                # 持续从消息队列获取结果并推送
                while not session.message_queue.is_closed:
                    # 定期检查客户端是否已断开（减少检查频率）
                    disconnect_check_counter += 1
                    if disconnect_check_counter >= disconnect_check_interval:
                        disconnect_check_counter = 0
                        if await request.is_disconnected():
                            session_logger.info("Client disconnected from SSE stream")
                            if sse_error_handler:
                                sse_error_handler.handle_client_disconnect(session_key)
                            break

                    # 从队列获取结果，超时时间用于检测客户端断开和发送心跳
                    result = await session.message_queue.get_result(timeout=1)

                    if result:
                        # 将结果转换为JSON字符串，使用json.dumps确保正确格式化
                        result_json = json.dumps(result.to_dict())
                        # 发送SSE事件
                        yield f"event: result\ndata: {result_json}\n\n"

                        # 更新会话活动时间
                        session.update_activity()

                        # 有数据时不需要额外延迟，立即处理下一个
                        continue

                    # 没有结果时的处理
                    current_time = time.time()

                    # 只在需要时发送心跳
                    if current_time - last_heartbeat >= heartbeat_interval:
                        yield f"event: heartbeat\ndata: {json.dumps({'timestamp': datetime.now().isoformat()})}\n\n"
                        last_heartbeat = current_time
                
            except asyncio.CancelledError:
                session_logger.info("SSE stream was cancelled")
            except Exception as e:
                session_logger.error(f"Error in SSE stream: {e}")
                if sse_error_handler:
                    sse_error_handler.handle_connection_error(session_key, e)
                # 发送错误事件
                yield f"event: error\ndata: {json.dumps({'error': str(e)})}\n\n"
            finally:
                # 发送关闭事件
                yield f"event: close\ndata: {json.dumps({'session_key': session_key})}\n\n"
                
                session_logger.info("SSE stream closed")
        
        # 返回SSE流响应
        return StreamingResponse(
            event_generator(),
            media_type="text/event-stream",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "X-Accel-Buffering": "no"  # 禁用Nginx缓冲
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        session_logger.error(f"Error establishing SSE connection: {e}")
        if sse_error_handler:
            sse_error_handler.handle_connection_error(session_key, e)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )


@app.exception_handler(Exception)
async def general_exception_handler(request, exc):
    """处理通用异常"""
    detail = api_error_handler.handle_internal_error(exc)
    raise HTTPException(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        detail=detail
    )


if __name__ == "__main__":
    import uvicorn
    
    # 启动FastAPI服务器
    uvicorn.run(
        "api_server:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )