# RTMP视频处理API配置文件

[rtmp]
# RTMP服务器配置
host = "0.0.0.0"
port = 1935

[fastapi]
# FastAPI服务器配置
host = "0.0.0.0"
port = 8000
reload = false
log_level = "info"
cors_origins = ["*"]

[session]
# 会话管理配置
timeout = 3600  # 会话超时时间（秒）
max_concurrent_sessions = 100  # 最大并发会话数
cleanup_interval = 300  # 会话清理间隔（秒）

[queue]
# 队列配置
max_size = 1000  # 消息队列最大大小
timeout = 1.0  # 队列操作超时时间（秒）
frame_queue_size = 100  # 帧队列大小

[processing]
# 视频处理配置
max_frame_rate = 30  # 最大处理帧率
frame_skip_threshold = 5  # 跳帧阈值
processing_timeout = 5.0  # 单帧处理超时时间（秒）

[logging]
# 日志配置
level = "DEBUG"
format = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
file_path = "logs/app.log"  # 启用文件日志
max_file_size = 10485760  # 日志文件最大大小（10MB）
backup_count = 5  # 日志文件备份数量