#!/usr/bin/env python3
"""
RTMP视频处理延迟测试客户端

本客户端用于测试视频处理的逐帧延迟：
1. 使用OpenCV获取默认摄像头的分辨率、帧率和画面
2. 用FFmpeg以原始数据逐帧推流
3. 记录发送每一帧的时间戳
4. 根据接收到的analysis_data中的frame_number判断得到的是第几帧数据
5. 计算逐帧延迟并绘制成图像
"""

import asyncio
import json
import time
import subprocess
import threading
import queue
from datetime import datetime
from typing import Optional, Dict, Any, List
import sys
import signal
import os

import cv2
import numpy as np
import matplotlib
import matplotlib.pyplot as plt
import matplotlib.animation as animation
from collections import deque
import httpx
import requests
import sseclient


class LatencyTestClient:
    """延迟测试客户端"""
    
    def __init__(self, api_base_url: str = "http://localhost:8000", 
                 rtmp_url: str = "rtmp://localhost:1935"):
        self.api_base_url = api_base_url.rstrip('/')
        self.rtmp_url = rtmp_url.rstrip('/')
        self.session_key: Optional[str] = None
        self.sse_client: Optional[sseclient.SSEClient] = None
        self.ffmpeg_process: Optional[subprocess.Popen] = None
        self.running = True
        
        # 摄像头信息
        self.camera_width = 640
        self.camera_height = 480
        self.camera_fps = 30
        
        # 延迟测试数据
        self.frame_send_times: Dict[int, float] = {}  # frame_number -> send_timestamp
        self.frame_latencies: List[tuple] = []  # (frame_number, latency_ms)
        self.current_frame_number = 0
        
        # 实时绘图数据
        self.latency_window = deque(maxlen=100)  # 最近100帧的延迟
        self.frame_window = deque(maxlen=100)    # 对应的帧号
        
        # 绘图相关
        self.fig = None
        self.ax = None
        self.line = None
        self.animation = None
        
        # 设置信号处理
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
    
    def _signal_handler(self, signum, frame):
        """处理退出信号"""
        print(f"\n收到退出信号 {signum}，正在清理...")
        self.running = False
        self.cleanup()
        sys.exit(0)
    
    def detect_camera_info(self) -> tuple:
        """检测默认摄像头的分辨率和帧率"""
        print("检测摄像头信息...")
        
        cap = cv2.VideoCapture(0)
        if not cap.isOpened():
            raise RuntimeError("无法打开默认摄像头")
        
        try:
            # 获取摄像头属性
            width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            fps = int(cap.get(cv2.CAP_PROP_FPS))
            
            # 如果fps为0，设置默认值
            if fps <= 0:
                fps = 30
            
            print(f"✓ 摄像头信息: {width}x{height} @ {fps}fps")
            
            # 测试读取一帧
            ret, frame = cap.read()
            if not ret:
                raise RuntimeError("无法从摄像头读取画面")
            
            print(f"✓ 摄像头画面正常，实际分辨率: {frame.shape[1]}x{frame.shape[0]}")
            
            return width, height, fps
            
        finally:
            cap.release()
    
    async def create_session(self, analysis_type: str = "opencv_preview") -> str:
        """创建视频处理会话"""
        print(f"创建会话，分析类型: {analysis_type}")
        
        async with httpx.AsyncClient() as client:
            try:
                response = await client.post(
                    f"{self.api_base_url}/api/session",
                    json={"analysis_type": analysis_type},
                    timeout=10.0
                )
                response.raise_for_status()
                
                data = response.json()
                self.session_key = data["session_key"]
                
                print(f"✓ 会话创建成功")
                print(f"  会话密钥: {self.session_key}")
                print(f"  分析类型: {data['analysis_type']}")
                
                return self.session_key
                
            except httpx.HTTPError as e:
                print(f"✗ 创建会话失败: {e}")
                raise
    
    def start_sse_connection(self) -> threading.Thread:
        """启动SSE连接接收处理结果"""
        if not self.session_key:
            raise ValueError("会话未创建")
        
        print("启动SSE连接...")
        
        def sse_handler():
            """SSE事件处理函数"""
            try:
                url = f"{self.api_base_url}/events/{self.session_key}"
                print(f"连接到SSE端点: {url}")
                
                response = requests.get(
                    url, 
                    stream=True,
                    headers={
                        'Accept': 'text/event-stream',
                        'Cache-Control': 'no-cache',
                        'Connection': 'keep-alive'
                    },
                    timeout=(10, None)
                )
                response.raise_for_status()
                
                self.sse_client = sseclient.SSEClient(response)
                print("✓ SSE连接建立成功")
                
                for event in self.sse_client.events():
                    if not self.running:
                        break
                    
                    try:
                        self._handle_sse_event(event)
                    except Exception as e:
                        print(f"处理SSE事件时出错: {e}")
                        
            except Exception as e:
                if self.running:
                    print(f"✗ SSE连接错误: {e}")
        
        sse_thread = threading.Thread(target=sse_handler, name="SSE-Handler", daemon=True)
        sse_thread.start()
        time.sleep(2)  # 等待连接建立
        
        return sse_thread
    
    def _handle_sse_event(self, event):
        """处理SSE事件"""
        try:
            if event.event == "connected":
                data = json.loads(event.data)
                print(f"✓ SSE连接确认: {data['session_key']}")
                
            elif event.event == "result":
                receive_time = time.time()
                data = json.loads(event.data)
                
                # 从analysis_data中获取frame_number
                analysis_data = data.get('analysis_data', {})
                frame_number = analysis_data.get('frame_number')
                
                if frame_number is not None and frame_number in self.frame_send_times:
                    send_time = self.frame_send_times[frame_number]
                    latency_ms = (receive_time - send_time) * 1000
                    
                    # 记录延迟数据
                    self.frame_latencies.append((frame_number, latency_ms))
                    self.latency_window.append(latency_ms)
                    self.frame_window.append(frame_number)
                    
                    # print(f"帧 {frame_number}: 延迟 {latency_ms:.2f}ms")
                    
                    # 清理旧的发送时间记录（保留最近1000帧）
                    if len(self.frame_send_times) > 1000:
                        old_frames = sorted(self.frame_send_times.keys())[:-1000]
                        for old_frame in old_frames:
                            del self.frame_send_times[old_frame]
                
        except json.JSONDecodeError as e:
            print(f"✗ 解析SSE数据失败: {e}")
        except Exception as e:
            print(f"✗ 处理SSE事件失败: {e}")

    def start_camera_stream_opencv(self, duration: int = 60):
        """使用OpenCV逐帧获取图像并通过FFmpeg推流"""
        if not self.session_key:
            raise ValueError("会话未创建")

        # 检测摄像头信息
        self.camera_width, self.camera_height, self.camera_fps = self.detect_camera_info()

        rtmp_stream_url = f"{self.rtmp_url}/live/{self.session_key}"
        print(f"启动OpenCV+FFmpeg推流到: {rtmp_stream_url}")
        print(f"分辨率: {self.camera_width}x{self.camera_height} @ {self.camera_fps}fps")

        # 启动OpenCV摄像头捕获和FFmpeg推流
        self._start_opencv_ffmpeg_stream(rtmp_stream_url, duration)

        return True

    def _start_opencv_ffmpeg_stream(self, rtmp_url: str, duration: int):
        """启动OpenCV摄像头捕获和FFmpeg推流的线程"""
        def stream_worker():
            """推流工作线程"""
            cap = None
            ffmpeg_process = None

            try:
                # 初始化OpenCV摄像头
                print("初始化OpenCV摄像头...")
                cap = cv2.VideoCapture(0)
                if not cap.isOpened():
                    raise RuntimeError("无法打开摄像头")

                # 设置摄像头参数
                cap.set(cv2.CAP_PROP_FRAME_WIDTH, self.camera_width)
                cap.set(cv2.CAP_PROP_FRAME_HEIGHT, self.camera_height)
                cap.set(cv2.CAP_PROP_FPS, self.camera_fps)

                # 获取实际参数
                actual_width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
                actual_height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
                actual_fps = cap.get(cv2.CAP_PROP_FPS)

                print(f"实际摄像头参数: {actual_width}x{actual_height} @ {actual_fps}fps")

                # 启动FFmpeg进程，从stdin接收原始视频数据
                ffmpeg_cmd = [
                    "ffmpeg",
                    "-y",  # 覆盖输出文件
                    "-f", "rawvideo",  # 输入格式为原始视频
                    "-vcodec", "rawvideo",
                    "-pix_fmt", "bgr24",  # OpenCV默认使用BGR格式
                    "-s", f"{actual_width}x{actual_height}",  # 输入分辨率
                    "-r", str(self.camera_fps),  # 输入帧率
                    "-i", "-",  # 从stdin读取
                    "-c:v", "libx264",  # 视频编码器
                    "-preset", "ultrafast",  # 编码预设
                    "-tune", "zerolatency",  # 零延迟调优
                    "-g", "1",  # 每帧都是关键帧
                    "-sc_threshold", "0",  # 禁用场景切换检测
                    "-f", "flv",  # 输出格式
                    rtmp_url
                ]

                print("启动FFmpeg推流进程...")
                ffmpeg_process = subprocess.Popen(
                    ffmpeg_cmd,
                    stdin=subprocess.PIPE,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE
                )

                self.ffmpeg_process = ffmpeg_process
                print(f"✓ FFmpeg推流进程已启动 (PID: {ffmpeg_process.pid})")

                # 开始逐帧推流
                frame_interval = 1.0 / self.camera_fps
                start_time = time.time()
                frame_count = 0

                print("开始逐帧推流...")

                while self.running and time.time() - start_time < duration:
                    frame_start_time = time.time()

                    # 读取一帧
                    ret, frame = cap.read()
                    if not ret:
                        print("无法读取摄像头帧")
                        break

                    frame_count += 1
                    self.current_frame_number = frame_count

                    # 记录发送时间戳
                    send_time = time.time()
                    self.frame_send_times[frame_count] = send_time

                    try:
                        # 将帧数据写入FFmpeg的stdin
                        ffmpeg_process.stdin.write(frame.tobytes())
                        ffmpeg_process.stdin.flush()

                        # 每100帧显示一次进度
                        if frame_count % 100 == 0:
                            elapsed = time.time() - start_time
                            print(f"已推流 {frame_count} 帧，耗时 {elapsed:.1f}s")

                    except BrokenPipeError:
                        print("FFmpeg进程已终止")
                        break
                    except Exception as e:
                        print(f"写入帧数据失败: {e}")
                        break

                    # 控制帧率
                    frame_end_time = time.time()
                    frame_duration = frame_end_time - frame_start_time
                    sleep_time = frame_interval - frame_duration

                    if sleep_time > 0:
                        time.sleep(sleep_time)

                print(f"推流完成，总共推送 {frame_count} 帧")

            except Exception as e:
                print(f"推流过程中发生错误: {e}")
                import traceback
                traceback.print_exc()

            finally:
                # 清理资源
                if cap:
                    cap.release()
                    print("摄像头已释放")

                if ffmpeg_process:
                    try:
                        ffmpeg_process.stdin.close()
                        ffmpeg_process.wait(timeout=5)
                        print("FFmpeg进程已正常结束")
                    except subprocess.TimeoutExpired:
                        ffmpeg_process.kill()
                        ffmpeg_process.wait()
                        print("FFmpeg进程已强制终止")
                    except Exception as e:
                        print(f"关闭FFmpeg进程时出错: {e}")

        # 在独立线程中运行推流
        stream_thread = threading.Thread(target=stream_worker, name="OpenCV-FFmpeg-Stream", daemon=True)
        stream_thread.start()

        return stream_thread



    def setup_realtime_plot(self):
        """设置实时延迟图表"""
        plt.ion()  # 开启交互模式
        self.fig, self.ax = plt.subplots(figsize=(12, 6))
        self.ax.set_title('实时视频处理延迟', fontsize=14)
        self.ax.set_xlabel('帧号')
        self.ax.set_ylabel('延迟 (ms)')
        self.ax.grid(True, alpha=0.3)

        # 初始化空线条
        self.line, = self.ax.plot([], [], 'b-', linewidth=2, label='延迟')
        self.ax.legend()

        # 设置动画
        self.animation = animation.FuncAnimation(
            self.fig, self._update_plot, interval=500, blit=True, save_count=50
        )

        plt.show(block=False)

    def _update_plot(self, frame):
        """更新延迟图表"""
        if len(self.latency_window) > 0:
            frames = list(self.frame_window)
            latencies = list(self.latency_window)

            self.line.set_data(frames, latencies)

            # 动态调整坐标轴
            if frames:
                self.ax.set_xlim(min(frames) - 5, max(frames) + 5)
                self.ax.set_ylim(min(latencies) - 10, max(latencies) + 10)

        return self.line,

    def save_latency_report(self, filename: str = None):
        """保存延迟测试报告"""
        if not self.frame_latencies:
            print("没有延迟数据可保存")
            return

        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"latency_report_{timestamp}.png"

        # 创建详细的延迟分析图表
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 10))

        frames, latencies = zip(*self.frame_latencies)

        # 延迟时间序列图
        ax1.plot(frames, latencies, 'b-', linewidth=1, alpha=0.7)
        ax1.set_title(f'视频处理延迟分析 (总帧数: {len(frames)})', fontsize=14)
        ax1.set_xlabel('帧号')
        ax1.set_ylabel('延迟 (ms)')
        ax1.grid(True, alpha=0.3)

        # 添加统计信息
        avg_latency = np.mean(latencies)
        min_latency = np.min(latencies)
        max_latency = np.max(latencies)
        std_latency = np.std(latencies)

        ax1.axhline(y=avg_latency, color='r', linestyle='--', label=f'平均延迟: {avg_latency:.2f}ms')
        ax1.legend()

        # 延迟分布直方图
        ax2.hist(latencies, bins=50, alpha=0.7, color='skyblue', edgecolor='black')
        ax2.set_title('延迟分布直方图', fontsize=14)
        ax2.set_xlabel('延迟 (ms)')
        ax2.set_ylabel('频次')
        ax2.axvline(x=avg_latency, color='r', linestyle='--', label=f'平均: {avg_latency:.2f}ms')
        ax2.grid(True, alpha=0.3)
        ax2.legend()

        # 添加统计文本
        stats_text = f"""统计信息:
平均延迟: {avg_latency:.2f} ms
最小延迟: {min_latency:.2f} ms
最大延迟: {max_latency:.2f} ms
标准差: {std_latency:.2f} ms
总帧数: {len(frames)}"""

        fig.text(0.02, 0.02, stats_text, fontsize=10,
                bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgray"))

        plt.tight_layout()
        plt.savefig(filename, dpi=300, bbox_inches='tight')
        print(f"✓ 延迟报告已保存到: {filename}")

        # 打印统计信息到控制台
        print(f"\n延迟统计:")
        print(f"  平均延迟: {avg_latency:.2f} ms")
        print(f"  最小延迟: {min_latency:.2f} ms")
        print(f"  最大延迟: {max_latency:.2f} ms")
        print(f"  标准差: {std_latency:.2f} ms")
        print(f"  总帧数: {len(frames)}")

    def cleanup(self):
        """清理资源"""
        print("清理资源...")
        self.running = False

        # 停止动画
        if self.animation and hasattr(self.animation, 'event_source'):
            try:
                self.animation.event_source.stop()
            except Exception:
                pass

        # 关闭图表
        if self.fig:
            try:
                plt.close(self.fig)
            except Exception:
                pass

        # 停止ffmpeg进程
        if self.ffmpeg_process and self.ffmpeg_process.poll() is None:
            print("  停止推流...")
            self.ffmpeg_process.terminate()
            try:
                self.ffmpeg_process.wait(timeout=5)
                print("  ✓ 推流已停止")
            except subprocess.TimeoutExpired:
                print("  ⚠ 推流强制终止")
                self.ffmpeg_process.kill()
                self.ffmpeg_process.wait()

        # 关闭SSE连接
        if self.sse_client:
            try:
                self.sse_client.close()
            except:
                pass

        print("✓ 资源清理完成")

    async def delete_session(self):
        """删除会话"""
        if not self.session_key:
            return

        print(f"删除会话: {self.session_key}")

        async with httpx.AsyncClient() as client:
            try:
                response = await client.delete(
                    f"{self.api_base_url}/api/session/{self.session_key}",
                    timeout=10.0
                )
                response.raise_for_status()
                print("✓ 会话删除成功")

            except httpx.HTTPError as e:
                print(f"✗ 删除会话失败: {e}")


async def main():
    """主函数 - 延迟测试流程"""
    print("=" * 60)
    print("RTMP视频处理延迟测试客户端")
    print("=" * 60)

    # 创建客户端
    client = LatencyTestClient()

    try:
        # 1. 检测摄像头
        print("\n1. 检测摄像头信息")
        client.detect_camera_info()

        # 2. 创建会话
        print("\n2. 创建处理会话")
        await client.create_session("opencv_preview")

        # 3. 启动SSE连接
        print("\n3. 启动SSE连接")
        sse_thread = client.start_sse_connection()

        # 4. 设置实时图表
        print("\n4. 设置实时延迟图表")
        client.setup_realtime_plot()

        # 5. 询问测试时长
        duration_input = input("\n测试时长（秒，默认60）: ").strip()
        try:
            duration = int(duration_input) if duration_input else 60
        except ValueError:
            duration = 60

        # 6. 启动OpenCV摄像头推流
        print(f"\n5. 启动OpenCV摄像头推流 (时长: {duration}秒)")
        client.start_camera_stream_opencv(duration)

        # 7. 等待测试完成
        print(f"\n6. 等待测试完成...")
        print("实时延迟图表已显示，按Ctrl+C可提前结束测试")

        # 等待推流完成（等待指定的duration时间）
        print(f"等待推流完成 ({duration}秒)...")
        for i in range(duration):
            if not client.running:
                break
            await asyncio.sleep(1)
            if (i + 1) % 10 == 0:  # 每10秒显示一次进度
                print(f"已运行 {i + 1}/{duration} 秒，当前接收到 {len(client.frame_latencies)} 个延迟数据")

        # 等待接收剩余结果
        print("\n等待接收剩余结果...")
        await asyncio.sleep(5)

        # 8. 保存测试报告
        print("\n7. 保存测试报告")
        client.save_latency_report()

        # 9. 清理资源
        print("\n8. 清理资源")
        client.cleanup()

        # 10. 删除会话
        print("\n9. 删除会话")
        await client.delete_session()

        print("\n" + "=" * 60)
        print("延迟测试完成！")
        print("=" * 60)

        # 保持图表显示
        input("按回车键退出...")

    except KeyboardInterrupt:
        print("\n用户中断测试")
        client.cleanup()
        await client.delete_session()
    except Exception as e:
        print(f"\n✗ 测试过程中发生错误: {e}")
        client.cleanup()
        await client.delete_session()
        raise


if __name__ == "__main__":
    # 检查依赖
    try:
        import cv2
        import matplotlib.pyplot as plt
        import numpy as np
        import httpx
        import requests
        import sseclient
    except ImportError as e:
        print(f"缺少依赖库: {e}")
        print("请安装依赖: pip install opencv-python matplotlib numpy httpx requests sseclient-py")
        sys.exit(1)
    matplotlib.rc("font", family='Microsoft YaHei')

    # 运行测试
    asyncio.run(main())
