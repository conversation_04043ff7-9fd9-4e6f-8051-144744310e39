import asyncio
import threading
from typing import Optional

from pyrtmp.rtmp import RTMPProtocol, SimpleRTMPServer

from logging_config import get_logger, get_error_handler, log_errors, log_operation

# 初始化日志和错误处理
logger = get_logger(__name__)
rtmp_error_handler = get_error_handler("rtmp")


class RtmpServerManager:
    """
    RTMP 服务器的高级管理器。
    使用VideoProcessingManager作为控制器，支持会话密钥验证。
    """
    def __init__(self, host: str = '0.0.0.0', port: int = 1935, controller=None):
        self._host = host
        self._port = port
        self._controller = controller  # VideoProcessingManager实例
        self._server_task: Optional[asyncio.Task] = None
        self._loop: Optional[asyncio.AbstractEventLoop] = None
        self._thread: Optional[threading.Thread] = None
        self._is_running = False
        self._lock = threading.Lock()

    @property
    def is_running(self) -> bool:
        """检查服务器是否正在运行"""
        return self._is_running
    
    def set_controller(self, controller):
        """
        设置VideoProcessingManager控制器
        
        Args:
            controller: VideoProcessingManager实例，用于处理RTMP回调和会话验证
        """
        self._controller = controller
        logger.info("RTMP server controller set to VideoProcessingManager")

    def _run_server(self):
        """在独立的线程中运行 asyncio 事件循环。"""
        self._loop = asyncio.new_event_loop()
        asyncio.set_event_loop(self._loop)
        
        class SessionAuthRTMPServer(SimpleRTMPServer):
            """
            支持会话密钥验证的RTMP服务器
            使用VideoProcessingManager作为控制器，实现基于会话密钥的流密钥验证和路由
            """
            def __init__(self, controller):
                super().__init__()
                self.controller = controller
            
            async def create(self, host: str, port: int):
                """创建RTMP服务器"""
                loop = asyncio.get_event_loop()
                self.server = await loop.create_server(
                    lambda: RTMPProtocol(controller=self.controller),
                    host=host,
                    port=port,
                )
                logger.info("Created RTMP server with session authentication support")

        async def main():
            if not self._controller:
                logger.error("No controller set for RTMP server")
                return
                
            try:
                server = SessionAuthRTMPServer(self._controller)
                await server.create(host=self._host, port=self._port)
                logger.info(f"RTMP 服务器已在 {self._host}:{self._port} 启动。")
                logger.info("使用VideoProcessingManager作为控制器，支持会话密钥验证")
                logger.info("客户端应使用会话密钥作为RTMP流密钥进行推流")
                
                self._is_running = True
                await server.start()
                await server.wait_closed()
                self._is_running = False
                logger.info("RTMP server stopped")
            except Exception as e:
                logger.error(f"Error in RTMP server: {e}", exc_info=True)
                self._is_running = False

        self._server_task = self._loop.create_task(main())
        try:
            self._loop.run_until_complete(self._server_task)
        except asyncio.CancelledError:
            logger.info("服务器任务被取消。")
        except Exception as e:
            logger.error(f"Error running RTMP server: {e}", exc_info=True)
        finally:
            self._loop.close()
            logger.info("Asyncio 事件循环已关闭。")

    def start(self):
        """在后台线程中启动 RTMP 服务器。"""
        with self._lock:
            if self._is_running:
                logger.warning("服务器已在运行中。")
                return
            self._thread = threading.Thread(target=self._run_server, daemon=True)
            self._thread.start()

    def stop(self):
        """停止 RTMP 服务器。"""
        with self._lock:
            if not self._is_running or not self._loop or not self._server_task:
                logger.warning("服务器未运行。")
                return
            
            logger.info("正在停止服务器...")
            self._loop.call_soon_threadsafe(self._server_task.cancel)
            
            if self._thread and self._thread.is_alive():
                self._thread.join(timeout=5)
            
            self._is_running = False
            logger.info("服务器已停止。")