#!/usr/bin/env python3
"""
SSE性能测试脚本
用于测试优化后的SSE发送速度
"""
import asyncio
import aiohttp
import json
import time
import numpy as np
from datetime import datetime
from typing import List, Dict, Any


class SSEPerformanceTester:
    """SSE性能测试器"""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.session_key = None
        self.received_events = []
        self.start_time = None
        self.end_time = None
    
    async def create_session(self, analysis_type: str = "dummy") -> str:
        """创建测试会话"""
        async with aiohttp.ClientSession() as session:
            async with session.post(
                f"{self.base_url}/api/session",
                json={"analysis_type": analysis_type}
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    self.session_key = data["session_key"]
                    print(f"Created session: {self.session_key}")
                    return self.session_key
                else:
                    raise Exception(f"Failed to create session: {response.status}")
    
    async def send_frames(self, num_frames: int = 100, fps: int = 30):
        """模拟发送视频帧"""
        if not self.session_key:
            raise Exception("No session created")
        
        # 获取会话实例（这里需要直接访问session_manager）
        from server.session_manager import session_manager
        session = session_manager.get_session(self.session_key)
        
        if not session:
            raise Exception("Session not found")
        
        # 启动处理
        session.start_processing()
        
        print(f"Sending {num_frames} frames at {fps} FPS...")
        frame_interval = 1.0 / fps
        
        for i in range(num_frames):
            # 创建测试帧
            frame = np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)
            
            # 发送帧到处理队列
            session.frame_queue.put_frame(frame)
            
            # 控制发送速度
            await asyncio.sleep(frame_interval)
            
            if (i + 1) % 10 == 0:
                print(f"Sent {i + 1} frames")
        
        print(f"Finished sending {num_frames} frames")
    
    async def listen_sse_events(self, duration: int = 10):
        """监听SSE事件"""
        if not self.session_key:
            raise Exception("No session created")
        
        print(f"Listening to SSE events for {duration} seconds...")
        self.start_time = time.time()
        self.received_events = []
        
        async with aiohttp.ClientSession() as session:
            async with session.get(
                f"{self.base_url}/events/{self.session_key}",
                headers={"Accept": "text/event-stream"}
            ) as response:
                if response.status != 200:
                    raise Exception(f"Failed to connect to SSE: {response.status}")
                
                async for line in response.content:
                    line = line.decode('utf-8').strip()
                    
                    if line.startswith('event:'):
                        event_type = line.split(':', 1)[1].strip()
                    elif line.startswith('data:'):
                        data = line.split(':', 1)[1].strip()
                        
                        # 记录事件
                        event_info = {
                            'timestamp': time.time(),
                            'type': event_type if 'event_type' in locals() else 'unknown',
                            'data': data
                        }
                        self.received_events.append(event_info)
                        
                        # 只统计result事件
                        if event_type == 'result':
                            elapsed = time.time() - self.start_time
                            result_count = len([e for e in self.received_events if e['type'] == 'result'])
                            print(f"Received result #{result_count} at {elapsed:.2f}s")
                    
                    # 检查是否达到测试时间
                    if time.time() - self.start_time >= duration:
                        break
        
        self.end_time = time.time()
    
    def analyze_performance(self) -> Dict[str, Any]:
        """分析性能数据"""
        if not self.received_events or not self.start_time or not self.end_time:
            return {"error": "No data to analyze"}
        
        total_duration = self.end_time - self.start_time
        result_events = [e for e in self.received_events if e['type'] == 'result']
        heartbeat_events = [e for e in self.received_events if e['type'] == 'heartbeat']
        
        # 计算发送速率
        if len(result_events) > 1:
            first_result_time = result_events[0]['timestamp']
            last_result_time = result_events[-1]['timestamp']
            result_duration = last_result_time - first_result_time
            avg_fps = len(result_events) / result_duration if result_duration > 0 else 0
        else:
            avg_fps = 0
        
        # 计算延迟分布
        if len(result_events) > 1:
            intervals = []
            for i in range(1, len(result_events)):
                interval = result_events[i]['timestamp'] - result_events[i-1]['timestamp']
                intervals.append(interval)
            
            min_interval = min(intervals)
            max_interval = max(intervals)
            avg_interval = sum(intervals) / len(intervals)
        else:
            min_interval = max_interval = avg_interval = 0
        
        return {
            "total_duration": total_duration,
            "total_events": len(self.received_events),
            "result_events": len(result_events),
            "heartbeat_events": len(heartbeat_events),
            "average_fps": avg_fps,
            "min_interval": min_interval,
            "max_interval": max_interval,
            "avg_interval": avg_interval,
            "events_per_second": len(self.received_events) / total_duration
        }
    
    async def cleanup(self):
        """清理测试会话"""
        if self.session_key:
            async with aiohttp.ClientSession() as session:
                async with session.delete(f"{self.base_url}/api/session/{self.session_key}") as response:
                    if response.status == 200:
                        print(f"Cleaned up session: {self.session_key}")
                    else:
                        print(f"Failed to cleanup session: {response.status}")


async def run_performance_test():
    """运行性能测试"""
    tester = SSEPerformanceTester()
    
    try:
        # 1. 创建会话
        await tester.create_session("dummy")
        
        # 2. 并发执行：发送帧和监听事件
        send_task = asyncio.create_task(tester.send_frames(num_frames=200, fps=60))
        listen_task = asyncio.create_task(tester.listen_sse_events(duration=15))
        
        # 等待两个任务完成
        await asyncio.gather(send_task, listen_task, return_exceptions=True)
        
        # 3. 分析性能
        performance = tester.analyze_performance()
        
        print("\n" + "="*50)
        print("PERFORMANCE ANALYSIS")
        print("="*50)
        print(f"Total Duration: {performance['total_duration']:.2f}s")
        print(f"Total Events: {performance['total_events']}")
        print(f"Result Events: {performance['result_events']}")
        print(f"Heartbeat Events: {performance['heartbeat_events']}")
        print(f"Average FPS: {performance['average_fps']:.2f}")
        print(f"Events per Second: {performance['events_per_second']:.2f}")
        print(f"Min Interval: {performance['min_interval']:.4f}s")
        print(f"Max Interval: {performance['max_interval']:.4f}s")
        print(f"Avg Interval: {performance['avg_interval']:.4f}s")
        
        # 判断性能是否改善
        if performance['average_fps'] > 20:
            print("\n✅ PERFORMANCE GOOD: SSE sending rate > 20 FPS")
        elif performance['average_fps'] > 10:
            print("\n⚠️  PERFORMANCE MODERATE: SSE sending rate 10-20 FPS")
        else:
            print("\n❌ PERFORMANCE POOR: SSE sending rate < 10 FPS")
    
    except Exception as e:
        print(f"Test failed: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 4. 清理
        await tester.cleanup()


if __name__ == "__main__":
    print("Starting SSE Performance Test...")
    print("Make sure the server is running on http://localhost:8000")
    asyncio.run(run_performance_test())
