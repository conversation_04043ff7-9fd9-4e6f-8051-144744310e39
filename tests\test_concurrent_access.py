"""
测试并发访问和异常情况
"""
import asyncio
import pytest
import threading
import time
import numpy as np
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from unittest.mock import Mock, patch

from server.session_manager import SessionManager, ProcessorFactory
from models import SessionQueue, FrameQueue, ProcessingResult, SessionStatus
from datetime import datetime


class TestConcurrentSessionManager:
    """测试SessionManager的并发访问"""
    
    @pytest.mark.asyncio
    async def test_concurrent_session_creation(self):
        """测试并发创建会话"""
        manager = SessionManager(session_timeout=30, max_concurrent_sessions=50)
        manager.start()
        
        try:
            # 并发创建会话
            async def create_session(i):
                return manager.create_session("dummy")
            
            # 创建20个并发任务
            tasks = [create_session(i) for i in range(20)]
            session_keys = await asyncio.gather(*tasks)
            
            # 验证所有会话都创建成功且唯一
            assert len(session_keys) == 20
            assert len(set(session_keys)) == 20  # 所有session_key都是唯一的
            assert manager.get_session_count() == 20
            
            # 验证每个会话都可以正常获取
            for session_key in session_keys:
                session = manager.get_session(session_key)
                assert session is not None
                assert session.session_key == session_key
                
        finally:
            await manager.stop()
    
    @pytest.mark.asyncio
    async def test_concurrent_session_operations(self):
        """测试并发会话操作"""
        manager = SessionManager(session_timeout=30, max_concurrent_sessions=100)
        manager.start()
        
        try:
            # 先创建一些会话
            session_keys = []
            for i in range(10):
                key = manager.create_session("dummy")
                session_keys.append(key)
            
            # 并发操作：获取、状态查询、移除
            async def get_session_info(session_key):
                session = manager.get_session(session_key)
                status = manager.get_session_status(session_key)
                return session, status
            
            async def remove_some_sessions():
                # 移除一半会话
                for i in range(0, len(session_keys), 2):
                    manager.remove_session(session_keys[i])
            
            # 并发执行操作
            get_tasks = [get_session_info(key) for key in session_keys]
            remove_task = remove_some_sessions()
            
            # 等待所有操作完成
            results = await asyncio.gather(*get_tasks, remove_task, return_exceptions=True)
            
            # 验证没有异常
            for result in results[:-1]:  # 除了remove_task
                if isinstance(result, Exception):
                    pytest.fail(f"Unexpected exception: {result}")
            
            # 验证剩余会话数量
            remaining_count = manager.get_session_count()
            assert remaining_count == 5  # 移除了一半
            
        finally:
            await manager.stop()
    
    def test_thread_safe_session_operations(self):
        """测试线程安全的会话操作"""
        manager = SessionManager(session_timeout=30, max_concurrent_sessions=100)
        manager.start()
        
        try:
            created_sessions = []
            errors = []
            
            def create_sessions_worker(worker_id, count):
                """工作线程：创建会话"""
                try:
                    for i in range(count):
                        session_key = manager.create_session("dummy")
                        created_sessions.append(session_key)
                        time.sleep(0.001)  # 小延迟模拟实际情况
                except Exception as e:
                    errors.append(e)
            
            def access_sessions_worker(worker_id):
                """工作线程：访问会话"""
                try:
                    for _ in range(50):
                        # 获取会话列表
                        sessions = manager.list_sessions()
                        if sessions:
                            # 随机访问一个会话
                            session_info = sessions[0]
                            session = manager.get_session(session_info["session_key"])
                        time.sleep(0.001)
                except Exception as e:
                    errors.append(e)
            
            # 启动多个线程
            with ThreadPoolExecutor(max_workers=8) as executor:
                futures = []
                
                # 创建会话的线程
                for i in range(3):
                    future = executor.submit(create_sessions_worker, i, 10)
                    futures.append(future)
                
                # 访问会话的线程
                for i in range(3):
                    future = executor.submit(access_sessions_worker, i)
                    futures.append(future)
                
                # 等待所有线程完成
                for future in as_completed(futures):
                    future.result()
            
            # 验证结果
            assert len(errors) == 0, f"Errors occurred: {errors}"
            assert len(created_sessions) == 30
            assert len(set(created_sessions)) == 30  # 所有session_key都是唯一的
            
        finally:
            # 使用asyncio运行停止操作
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                loop.run_until_complete(manager.stop())
            finally:
                loop.close()
    
    @pytest.mark.asyncio
    async def test_max_sessions_under_load(self):
        """测试高负载下的最大会话数限制"""
        manager = SessionManager(session_timeout=30, max_concurrent_sessions=5)
        manager.start()
        
        try:
            successful_creations = []
            failed_creations = []
            
            async def try_create_session(i):
                try:
                    session_key = manager.create_session("dummy")
                    successful_creations.append(session_key)
                    return session_key
                except ValueError as e:
                    failed_creations.append(str(e))
                    return None
            
            # 尝试创建超过限制的会话
            tasks = [try_create_session(i) for i in range(20)]
            results = await asyncio.gather(*tasks)
            
            # 验证只有5个成功创建
            assert len(successful_creations) == 5
            assert len(failed_creations) == 15
            assert manager.get_session_count() == 5
            
            # 验证错误消息正确
            for error_msg in failed_creations:
                assert "Maximum concurrent sessions" in error_msg
                
        finally:
            await manager.stop()


class TestConcurrentQueueOperations:
    """测试队列的并发操作"""
    
    @pytest.mark.asyncio
    async def test_concurrent_message_queue_operations(self):
        """测试消息队列的并发操作"""
        queue = SessionQueue(max_size=100)
        
        results_put = []
        results_got = []
        
        async def producer(producer_id, count):
            """生产者协程"""
            for i in range(count):
                result = ProcessingResult(
                    session_key=f"session-{producer_id}",
                    timestamp=datetime.now(),
                    frame_info={"producer": producer_id, "index": i},
                    analysis_data={"data": f"producer-{producer_id}-item-{i}"}
                )
                await queue.put_result(result)
                results_put.append(result)
                await asyncio.sleep(0.001)  # 小延迟
        
        async def consumer(consumer_id, count):
            """消费者协程"""
            for _ in range(count):
                result = await queue.get_result(timeout=2.0)
                if result:
                    results_got.append(result)
                await asyncio.sleep(0.001)  # 小延迟
        
        # 启动多个生产者和消费者
        tasks = []
        
        # 3个生产者，每个生产10个结果
        for i in range(3):
            task = asyncio.create_task(producer(i, 10))
            tasks.append(task)
        
        # 2个消费者，每个消费15个结果
        for i in range(2):
            task = asyncio.create_task(consumer(i, 15))
            tasks.append(task)
        
        # 等待所有任务完成
        await asyncio.gather(*tasks)
        
        # 验证结果
        assert len(results_put) == 30
        assert len(results_got) == 30
        
        # 验证所有放入的结果都被获取了
        put_data = [r.analysis_data["data"] for r in results_put]
        got_data = [r.analysis_data["data"] for r in results_got]
        assert set(put_data) == set(got_data)
    
    @pytest.mark.asyncio
    async def test_concurrent_frame_queue_operations(self):
        """测试帧队列的并发操作"""
        queue = FrameQueue(max_size=50)
        
        frames_put = []
        frames_got = []
        
        def producer(producer_id, count):
            """生产者（同步）"""
            for i in range(count):
                frame = np.ones((50, 50, 3), dtype=np.uint8) * (producer_id * 10 + i)
                queue.put_frame(frame)
                frames_put.append(frame)
                time.sleep(0.001)  # 小延迟
        
        async def consumer(consumer_id, count):
            """消费者（异步）"""
            for _ in range(count):
                frame = await queue.get_frame(timeout=2.0)
                if frame is not None:
                    frames_got.append(frame)
                await asyncio.sleep(0.001)  # 小延迟
        
        # 在线程中运行生产者
        def run_producer(producer_id, count):
            producer(producer_id, count)
        
        # 启动生产者线程
        with ThreadPoolExecutor(max_workers=3) as executor:
            producer_futures = []
            for i in range(3):
                future = executor.submit(run_producer, i, 10)
                producer_futures.append(future)
            
            # 启动消费者协程
            consumer_tasks = []
            for i in range(2):
                task = asyncio.create_task(consumer(i, 15))
                consumer_tasks.append(task)
            
            # 等待生产者完成
            for future in producer_futures:
                future.result()
            
            # 等待消费者完成
            await asyncio.gather(*consumer_tasks)
        
        # 验证结果
        assert len(frames_put) == 30
        assert len(frames_got) == 30
        
        # 验证帧数据的完整性
        for frame in frames_got:
            assert frame.shape == (50, 50, 3)
            assert frame.dtype == np.uint8
    
    @pytest.mark.asyncio
    async def test_queue_overflow_behavior(self):
        """测试队列溢出行为"""
        queue = SessionQueue(max_size=5)
        
        # 快速放入大量结果
        results = []
        for i in range(20):
            result = ProcessingResult(
                session_key="test",
                timestamp=datetime.now(),
                frame_info={},
                analysis_data={"index": i}
            )
            await queue.put_result(result)
            results.append(result)
        
        # 获取所有可用结果
        retrieved_results = []
        while True:
            result = await queue.get_result(timeout=0.1)
            if result is None:
                break
            retrieved_results.append(result)
        
        # 验证只保留了最新的结果
        assert len(retrieved_results) <= 5
        
        # 验证是最新的结果
        if retrieved_results:
            indices = [r.analysis_data["index"] for r in retrieved_results]
            assert max(indices) == 19  # 最新的结果应该在


class TestExceptionHandling:
    """测试异常情况处理"""
    
    @pytest.mark.asyncio
    async def test_session_manager_error_recovery(self):
        """测试会话管理器的错误恢复"""
        manager = SessionManager(session_timeout=30, max_concurrent_sessions=10)
        manager.start()
        
        try:
            # 正常创建会话
            session_key1 = manager.create_session("dummy")
            assert session_key1 is not None
            
            # 模拟处理器创建失败
            with patch.object(ProcessorFactory, 'create_processor', side_effect=Exception("Processor creation failed")):
                with pytest.raises(Exception, match="Processor creation failed"):
                    manager.create_session("dummy")
            
            # 验证管理器仍然可以正常工作
            session_key2 = manager.create_session("dummy")
            assert session_key2 is not None
            assert session_key2 != session_key1
            
            # 验证之前的会话仍然存在
            session1 = manager.get_session(session_key1)
            assert session1 is not None
            
        finally:
            await manager.stop()
    
    @pytest.mark.asyncio
    async def test_processor_error_handling(self):
        """测试处理器错误处理"""
        manager = SessionManager(session_timeout=30, max_concurrent_sessions=10)
        manager.start()
        
        try:
            # 创建会话
            session_key = manager.create_session("dummy")
            session = manager.get_session(session_key)
            
            # 启动处理
            session.start_processing()
            
            # 模拟处理器错误
            original_process_frame = session.processor.process_frame
            
            def failing_process_frame(frame):
                if hasattr(failing_process_frame, 'call_count'):
                    failing_process_frame.call_count += 1
                else:
                    failing_process_frame.call_count = 1
                
                # 第2次调用时抛出异常
                if failing_process_frame.call_count == 2:
                    raise Exception("Processing error")
                
                return original_process_frame(frame)
            
            session.processor.process_frame = failing_process_frame
            
            # 发送多个帧
            test_frame = np.random.randint(0, 255, (100, 100, 3), dtype=np.uint8)
            
            session.frame_queue.put_frame(test_frame)  # 第1帧：成功
            session.frame_queue.put_frame(test_frame)  # 第2帧：失败
            session.frame_queue.put_frame(test_frame)  # 第3帧：成功
            
            # 等待处理
            await asyncio.sleep(0.5)
            
            # 获取结果
            results = []
            for _ in range(3):
                result = await session.message_queue.get_result(timeout=1.0)
                if result:
                    results.append(result)
            
            # 验证结果
            assert len(results) == 3
            
            # 第1个和第3个结果应该成功，第2个应该有错误
            success_count = sum(1 for r in results if r.error is None)
            error_count = sum(1 for r in results if r.error is not None)
            
            assert success_count == 2
            assert error_count == 1
            
        finally:
            await manager.stop()
    
    @pytest.mark.asyncio
    async def test_queue_close_during_operations(self):
        """测试操作过程中关闭队列"""
        queue = SessionQueue(max_size=10)
        
        # 启动生产者
        async def producer():
            for i in range(100):
                result = ProcessingResult(
                    session_key="test",
                    timestamp=datetime.now(),
                    frame_info={},
                    analysis_data={"index": i}
                )
                await queue.put_result(result)
                await asyncio.sleep(0.01)
        
        # 启动消费者
        async def consumer():
            results = []
            while not queue.is_closed:
                result = await queue.get_result(timeout=0.1)
                if result:
                    results.append(result)
            return results
        
        # 启动任务
        producer_task = asyncio.create_task(producer())
        consumer_task = asyncio.create_task(consumer())
        
        # 等待一段时间后关闭队列
        await asyncio.sleep(0.2)
        queue.close()
        
        # 等待任务完成
        await asyncio.gather(producer_task, consumer_task, return_exceptions=True)
        
        # 验证队列已关闭
        assert queue.is_closed
        
        # 关闭后的操作应该被忽略
        result = ProcessingResult(
            session_key="test",
            timestamp=datetime.now(),
            frame_info={},
            analysis_data={}
        )
        await queue.put_result(result)  # 应该被忽略
        
        final_result = await queue.get_result()
        assert final_result is None
    
    @pytest.mark.asyncio
    async def test_memory_pressure_handling(self):
        """测试内存压力下的处理"""
        # 创建小容量队列模拟内存压力
        frame_queue = FrameQueue(max_size=3)
        message_queue = SessionQueue(max_size=3)
        
        # 快速生成大量数据
        large_frame = np.random.randint(0, 255, (1080, 1920, 3), dtype=np.uint8)
        
        # 放入大量帧数据
        for i in range(10):
            frame_queue.put_frame(large_frame)
        
        # 验证队列大小限制生效
        retrieved_frames = []
        while True:
            frame = await frame_queue.get_frame(timeout=0.1)
            if frame is None:
                break
            retrieved_frames.append(frame)
        
        # 应该只有最新的3帧
        assert len(retrieved_frames) <= 3
        
        # 测试消息队列的内存管理
        for i in range(10):
            result = ProcessingResult(
                session_key="test",
                timestamp=datetime.now(),
                frame_info={"large_data": "x" * 10000},  # 大数据
                analysis_data={"index": i}
            )
            await message_queue.put_result(result)
        
        # 验证消息队列大小限制
        retrieved_results = []
        while True:
            result = await message_queue.get_result(timeout=0.1)
            if result is None:
                break
            retrieved_results.append(result)
        
        assert len(retrieved_results) <= 3


if __name__ == "__main__":
    pytest.main([__file__, "-v"])