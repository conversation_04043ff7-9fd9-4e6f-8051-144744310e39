"""
配置管理系统测试
"""
import os
import tempfile
import pytest
from pathlib import Path
from unittest.mock import patch

from ..config import (
    Config, RTMPConfig, FastAPIConfig, SessionConfig, 
    QueueConfig, ProcessingConfig, LoggingConfig,
    load_config, get_config, reload_config
)


class TestRTMPConfig:
    """RTMP配置测试"""
    
    def test_default_values(self):
        """测试默认值"""
        config = RTMPConfig()
        assert config.host == "0.0.0.0"
        assert config.port == 1935
    
    def test_valid_host(self):
        """测试有效主机地址"""
        config = RTMPConfig(host="localhost")
        assert config.host == "localhost"
    
    def test_invalid_host(self):
        """测试无效主机地址"""
        with pytest.raises(ValueError):
            RTMPConfig(host="")
    
    def test_valid_port(self):
        """测试有效端口"""
        config = RTMPConfig(port=8080)
        assert config.port == 8080
    
    def test_invalid_port(self):
        """测试无效端口"""
        with pytest.raises(ValueError):
            RTMPConfig(port=0)
        
        with pytest.raises(ValueError):
            RTMPConfig(port=70000)


class TestFastAPIConfig:
    """FastAPI配置测试"""
    
    def test_default_values(self):
        """测试默认值"""
        config = FastAPIConfig()
        assert config.host == "0.0.0.0"
        assert config.port == 8000
        assert config.reload == False
        assert config.log_level == "info"
        assert config.cors_origins == ["*"]
    
    def test_valid_log_level(self):
        """测试有效日志级别"""
        config = FastAPIConfig(log_level="debug")
        assert config.log_level == "debug"
    
    def test_invalid_log_level(self):
        """测试无效日志级别"""
        with pytest.raises(ValueError):
            FastAPIConfig(log_level="invalid")


class TestSessionConfig:
    """会话配置测试"""
    
    def test_default_values(self):
        """测试默认值"""
        config = SessionConfig()
        assert config.timeout == 3600
        assert config.max_concurrent_sessions == 100
        assert config.cleanup_interval == 300
    
    def test_valid_timeout(self):
        """测试有效超时时间"""
        config = SessionConfig(timeout=1800)
        assert config.timeout == 1800
    
    def test_invalid_timeout(self):
        """测试无效超时时间"""
        with pytest.raises(ValueError):
            SessionConfig(timeout=30)


class TestQueueConfig:
    """队列配置测试"""
    
    def test_default_values(self):
        """测试默认值"""
        config = QueueConfig()
        assert config.max_size == 1000
        assert config.timeout == 1.0
        assert config.frame_queue_size == 100
    
    def test_valid_queue_size(self):
        """测试有效队列大小"""
        config = QueueConfig(max_size=500, frame_queue_size=50)
        assert config.max_size == 500
        assert config.frame_queue_size == 50
    
    def test_invalid_queue_size(self):
        """测试无效队列大小"""
        with pytest.raises(ValueError):
            QueueConfig(max_size=5)
        
        with pytest.raises(ValueError):
            QueueConfig(frame_queue_size=5)


class TestProcessingConfig:
    """处理配置测试"""
    
    def test_default_values(self):
        """测试默认值"""
        config = ProcessingConfig()
        assert config.max_frame_rate == 30
        assert config.frame_skip_threshold == 5
        assert config.processing_timeout == 5.0
    
    def test_valid_frame_rate(self):
        """测试有效帧率"""
        config = ProcessingConfig(max_frame_rate=15)
        assert config.max_frame_rate == 15
    
    def test_invalid_frame_rate(self):
        """测试无效帧率"""
        with pytest.raises(ValueError):
            ProcessingConfig(max_frame_rate=0)
        
        with pytest.raises(ValueError):
            ProcessingConfig(max_frame_rate=100)


class TestLoggingConfig:
    """日志配置测试"""
    
    def test_default_values(self):
        """测试默认值"""
        config = LoggingConfig()
        assert config.level == "INFO"
        assert config.file_path is None
        assert config.max_file_size == 10485760
        assert config.backup_count == 5
    
    def test_valid_level(self):
        """测试有效日志级别"""
        config = LoggingConfig(level="debug")
        assert config.level == "DEBUG"
    
    def test_invalid_level(self):
        """测试无效日志级别"""
        with pytest.raises(ValueError):
            LoggingConfig(level="invalid")


class TestConfig:
    """主配置类测试"""
    
    def test_default_config(self):
        """测试默认配置"""
        config = Config()
        assert isinstance(config.rtmp, RTMPConfig)
        assert isinstance(config.fastapi, FastAPIConfig)
        assert isinstance(config.session, SessionConfig)
        assert isinstance(config.queue, QueueConfig)
        assert isinstance(config.processing, ProcessingConfig)
        assert isinstance(config.logging, LoggingConfig)
    
    def test_from_toml_nonexistent_file(self):
        """测试不存在的TOML文件"""
        config = Config.from_toml("nonexistent.toml")
        # 应该返回默认配置
        assert config.rtmp.port == 1935
        assert config.fastapi.port == 8000
    
    def test_from_toml_valid_file(self):
        """测试有效的TOML文件"""
        toml_content = """
[rtmp]
host = "localhost"
port = 1936

[fastapi]
port = 8001
log_level = "debug"

[session]
timeout = 1800
"""
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.toml', delete=False) as f:
            f.write(toml_content)
            temp_path = f.name
        
        try:
            config = Config.from_toml(temp_path)
            assert config.rtmp.host == "localhost"
            assert config.rtmp.port == 1936
            assert config.fastapi.port == 8001
            assert config.fastapi.log_level == "debug"
            assert config.session.timeout == 1800
        finally:
            os.unlink(temp_path)
    
    def test_from_toml_invalid_file(self):
        """测试无效的TOML文件"""
        invalid_toml = "invalid toml content ["
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.toml', delete=False) as f:
            f.write(invalid_toml)
            temp_path = f.name
        
        try:
            with pytest.raises(ValueError):
                Config.from_toml(temp_path)
        finally:
            os.unlink(temp_path)
    
    def test_from_env(self):
        """测试从环境变量加载配置"""
        env_vars = {
            'APP_RTMP_HOST': 'localhost',
            'APP_RTMP_PORT': '1936',
            'APP_FASTAPI_PORT': '8001',
            'APP_FASTAPI_RELOAD': 'true',
            'APP_SESSION_TIMEOUT': '1800',
            'APP_QUEUE_MAX_SIZE': '500',
            'APP_LOGGING_LEVEL': 'DEBUG'
        }
        
        with patch.dict(os.environ, env_vars):
            config = Config.from_env()
            assert config.rtmp.host == 'localhost'
            assert config.rtmp.port == 1936
            assert config.fastapi.port == 8001
            assert config.fastapi.reload == True
            assert config.session.timeout == 1800
            assert config.queue.max_size == 500
            assert config.logging.level == 'DEBUG'
    
    def test_to_dict(self):
        """测试转换为字典"""
        config = Config()
        config_dict = config.to_dict()
        
        assert isinstance(config_dict, dict)
        assert 'rtmp' in config_dict
        assert 'fastapi' in config_dict
        assert 'session' in config_dict
        assert 'queue' in config_dict
        assert 'processing' in config_dict
        assert 'logging' in config_dict
    
    def test_setup_logging(self):
        """测试日志设置"""
        config = Config()
        # 这个测试主要确保方法不会抛出异常
        config.setup_logging()


class TestConfigModule:
    """配置模块测试"""
    
    def test_load_config_default(self):
        """测试加载默认配置"""
        # 重置全局配置
        from .. import config as config_module
        config_module.config = None
        
        loaded_config = load_config("nonexistent.toml", use_env=False)
        assert isinstance(loaded_config, Config)
        
        # 测试get_config
        retrieved_config = get_config()
        assert retrieved_config is loaded_config
    
    def test_get_config_not_initialized(self):
        """测试未初始化时获取配置"""
        from .. import config as config_module
        config_module.config = None
        
        with pytest.raises(RuntimeError):
            get_config()
    
    def test_reload_config(self):
        """测试重新加载配置"""
        # 先加载一次
        load_config("nonexistent.toml", use_env=False)
        
        # 重新加载
        reloaded_config = reload_config("nonexistent.toml", use_env=False)
        assert isinstance(reloaded_config, Config)


if __name__ == "__main__":
    # 配置日志
    from .. import logging
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    
    # 运行测试
    pytest.main([__file__, "-v"])